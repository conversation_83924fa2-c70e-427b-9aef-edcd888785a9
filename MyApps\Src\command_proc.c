#include "command_proc.h"

void CmdStopSample(void)
{
    sample_status.enable = 0;
    Usart1Printf("report:ok\r\n");
}

void CmdStartSample(void)
{
    sample_status.enable = 1;
    SampleProc();
}

void CmdGetData(void)
{
    Usart1Printf("report:ch0=%.2f,ch1=%.2f,ch2=%.2f\r\n", gd30_channels[0].voltage, gd30_channels[1].voltage, gd30_channels[2].voltage);
}

void Cmd_SetLimit(char *buffer)
{
    float ch0_input = 0.0f, ch1_input = 0.0f, ch2_input = 0.0f;
    int chars_read = 0;

    int result = sscanf(buffer, "command:set_limit:ch0=%f,ch1=%f,ch2=%f\r\n%n",
                        &ch0_input, &ch1_input, &ch2_input, &chars_read);
    if (result == 3 && chars_read == strlen(buffer))
    {
        if (TF_WriteLimit(ch0_input, ch1_input, ch2_input) == SUCCESS)
        {
            config_data[0].limit = ch0_input; config_data[1].limit = ch1_input; config_data[2].limit = ch2_input;
            Usart1Printf("report:ok\r\n");
        }
        else
        {
            Usart1Printf("report:write_failed\r\n");
        }
    }
    else
    {
        Usart1Printf("report:format_error\r\n");
    }
}

void Cmd_GetRatio(void)
{
    Usart1Printf("report:ch0ratio =%.2f,ch1ratio =%.2f,ch2ratio =%.2f\r\n", 
                 config_data[0].ratio, config_data[1].ratio, config_data[2].ratio);
}

void Cmd_SetRatio(char *buffer)
{
    float ch0_input = 0.0f, ch1_input = 0.0f, ch2_input = 0.0f;
    int chars_read = 0;

    int result = sscanf(buffer, "command:set_ratio:ch0=%f,ch1=%f,ch2=%f\r\n%n", 
                        &ch0_input, &ch1_input, &ch2_input, &chars_read);
    if (result == 3 && chars_read == strlen(buffer))
    {
        if (TF_WriteRatio(ch0_input, ch1_input, ch2_input) == SUCCESS)
        {
            config_data[0].ratio = ch0_input; config_data[1].ratio = ch1_input; config_data[2].ratio = ch2_input;
            Usart1Printf("report:ok\r\n");
        }
    }
}

void Cmd_SetRtc(char *buffer)
{
    rtc_parameter_struct current_time = {0};
    int chars_read = 0;

    int result = sscanf(buffer, "command:set_RTC=20%hhu-%hhu-%hhu %hhu:%hhu:%hhu\r\n%n", 
                        &current_time.year, &current_time.month, &current_time.date, &current_time.hour, 
                        &current_time.minute, &current_time.second, &chars_read);
    if (result == 6 && chars_read == strlen(buffer))
    {
        if (SetRtc(&current_time) == SUCCESS)
        {
            Usart1Printf("report:ok\r\n");
        }
    }
}

void Cmd_GetRtc(void)
{
    Usart1Printf("report:currentTime=20%hhu-%02hhu-%02hhu %02hhu:%02hhu:%02hhu\r\n",
                 ucRtc.year, ucRtc.month, ucRtc.date, ucRtc.hour, 
                 ucRtc.minute, ucRtc.second);
}

void Cmd_GetDeviceId()
{
    Usart1Printf("report:device_id=%s\r\n", device_id);
}

void CmdProc(char *buffer)
{
    if (strcmp(buffer, "command:get_device_id\r\n") == 0) Cmd_GetDeviceId();
    else if (strcmp(buffer, "command:get_RTC\r\n") == 0) Cmd_GetRtc();
    else if (strncmp(buffer, "command:set_RTC", 15) == 0) Cmd_SetRtc(buffer);
    else if (strncmp(buffer, "command:set_ratio", 17) == 0) Cmd_SetRatio(buffer);
    else if (strcmp(buffer, "command:get_ratio\r\n") == 0) Cmd_GetRatio();
    else if (strcmp(buffer, "command:get_data\r\n") == 0) CmdGetData();
    else if (strcmp(buffer, "command:start_sample\r\n") == 0) CmdStartSample();
    else if (strcmp(buffer, "command:stop_sample\r\n") == 0) CmdStopSample();
    else if (strncmp(buffer, "command:set_limit", 17) == 0) Cmd_SetLimit(buffer);
}
