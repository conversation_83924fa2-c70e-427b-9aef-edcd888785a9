Component: ARM Compiler 5.06 update 7 (build 960) Tool: armlink [4d3601]

==============================================================================

Section Cross References

    gd32f4xx_it.o(i.SDIO_IRQHandler) refers to sdio_sdcard.o(i.sd_interrupts_process) for sd_interrupts_process
    gd32f4xx_it.o(i.SysTick_Handler) refers to systick.o(i.delay_decrement) for delay_decrement
    gd32f4xx_it.o(i.SysTick_Handler) refers to gd32f470vet6_bsp.o(.data) for uwTick
    gd32f4xx_it.o(i.USART0_IRQHandler) refers to gd32f4xx_usart.o(i.usart_interrupt_flag_get) for usart_interrupt_flag_get
    gd32f4xx_it.o(i.USART0_IRQHandler) refers to gd32f4xx_usart.o(i.usart_interrupt_disable) for usart_interrupt_disable
    gd32f4xx_it.o(i.USART0_IRQHandler) refers to gd32f4xx_usart.o(i.usart_data_receive) for usart_data_receive
    gd32f4xx_it.o(i.USART0_IRQHandler) refers to gd32f4xx_dma.o(i.dma_transfer_number_get) for dma_transfer_number_get
    gd32f4xx_it.o(i.USART0_IRQHandler) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    gd32f4xx_it.o(i.USART0_IRQHandler) refers to rt_memclr.o(.text) for __aeabi_memclr
    gd32f4xx_it.o(i.USART0_IRQHandler) refers to gd32f4xx_dma.o(i.dma_channel_disable) for dma_channel_disable
    gd32f4xx_it.o(i.USART0_IRQHandler) refers to gd32f4xx_dma.o(i.dma_flag_clear) for dma_flag_clear
    gd32f4xx_it.o(i.USART0_IRQHandler) refers to gd32f4xx_dma.o(i.dma_transfer_number_config) for dma_transfer_number_config
    gd32f4xx_it.o(i.USART0_IRQHandler) refers to gd32f4xx_dma.o(i.dma_channel_enable) for dma_channel_enable
    gd32f4xx_it.o(i.USART0_IRQHandler) refers to gd32f4xx_usart.o(i.usart_interrupt_enable) for usart_interrupt_enable
    gd32f4xx_it.o(i.USART0_IRQHandler) refers to gd32f470vet6_bsp.o(.bss) for usart0_rx_buffer_dma
    gd32f4xx_it.o(i.USART0_IRQHandler) refers to gd32f470vet6_bsp.o(.data) for usart0_rx_flag
    gd32f4xx_it.o(i.USART1_IRQHandler) refers to gd32f4xx_usart.o(i.usart_interrupt_flag_get) for usart_interrupt_flag_get
    gd32f4xx_it.o(i.USART1_IRQHandler) refers to gd32f4xx_usart.o(i.usart_interrupt_disable) for usart_interrupt_disable
    gd32f4xx_it.o(i.USART1_IRQHandler) refers to gd32f4xx_usart.o(i.usart_data_receive) for usart_data_receive
    gd32f4xx_it.o(i.USART1_IRQHandler) refers to gd32f4xx_dma.o(i.dma_transfer_number_get) for dma_transfer_number_get
    gd32f4xx_it.o(i.USART1_IRQHandler) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    gd32f4xx_it.o(i.USART1_IRQHandler) refers to rt_memclr.o(.text) for __aeabi_memclr
    gd32f4xx_it.o(i.USART1_IRQHandler) refers to gd32f4xx_dma.o(i.dma_channel_disable) for dma_channel_disable
    gd32f4xx_it.o(i.USART1_IRQHandler) refers to gd32f4xx_dma.o(i.dma_flag_clear) for dma_flag_clear
    gd32f4xx_it.o(i.USART1_IRQHandler) refers to gd32f4xx_dma.o(i.dma_transfer_number_config) for dma_transfer_number_config
    gd32f4xx_it.o(i.USART1_IRQHandler) refers to gd32f4xx_dma.o(i.dma_channel_enable) for dma_channel_enable
    gd32f4xx_it.o(i.USART1_IRQHandler) refers to gd32f4xx_usart.o(i.usart_interrupt_enable) for usart_interrupt_enable
    gd32f4xx_it.o(i.USART1_IRQHandler) refers to gd32f470vet6_bsp.o(.bss) for usart1_rx_buffer_dma
    gd32f4xx_it.o(i.USART1_IRQHandler) refers to gd32f470vet6_bsp.o(.data) for usart1_rx_flag
    gd32f470vet6_bsp.o(i.AdcDmaInit) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    gd32f470vet6_bsp.o(i.AdcDmaInit) refers to gd32f4xx_dma.o(i.dma_deinit) for dma_deinit
    gd32f470vet6_bsp.o(i.AdcDmaInit) refers to gd32f4xx_dma.o(i.dma_single_data_mode_init) for dma_single_data_mode_init
    gd32f470vet6_bsp.o(i.AdcDmaInit) refers to gd32f4xx_dma.o(i.dma_circulation_enable) for dma_circulation_enable
    gd32f470vet6_bsp.o(i.AdcDmaInit) refers to gd32f4xx_dma.o(i.dma_channel_subperipheral_select) for dma_channel_subperipheral_select
    gd32f470vet6_bsp.o(i.AdcDmaInit) refers to gd32f4xx_dma.o(i.dma_channel_enable) for dma_channel_enable
    gd32f470vet6_bsp.o(i.AdcDmaInit) refers to gd32f470vet6_bsp.o(.bss) for adc0_value
    gd32f470vet6_bsp.o(i.AdcPeriphInit) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    gd32f470vet6_bsp.o(i.AdcPeriphInit) refers to gd32f4xx_adc.o(i.adc_clock_config) for adc_clock_config
    gd32f470vet6_bsp.o(i.AdcPeriphInit) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    gd32f470vet6_bsp.o(i.AdcPeriphInit) refers to gd32f470vet6_bsp.o(i.AdcDmaInit) for AdcDmaInit
    gd32f470vet6_bsp.o(i.AdcPeriphInit) refers to gd32f4xx_adc.o(i.adc_sync_mode_config) for adc_sync_mode_config
    gd32f470vet6_bsp.o(i.AdcPeriphInit) refers to gd32f4xx_adc.o(i.adc_special_function_config) for adc_special_function_config
    gd32f470vet6_bsp.o(i.AdcPeriphInit) refers to gd32f4xx_adc.o(i.adc_data_alignment_config) for adc_data_alignment_config
    gd32f470vet6_bsp.o(i.AdcPeriphInit) refers to gd32f4xx_adc.o(i.adc_channel_length_config) for adc_channel_length_config
    gd32f470vet6_bsp.o(i.AdcPeriphInit) refers to gd32f4xx_adc.o(i.adc_routine_channel_config) for adc_routine_channel_config
    gd32f470vet6_bsp.o(i.AdcPeriphInit) refers to gd32f4xx_adc.o(i.adc_external_trigger_source_config) for adc_external_trigger_source_config
    gd32f470vet6_bsp.o(i.AdcPeriphInit) refers to gd32f4xx_adc.o(i.adc_external_trigger_config) for adc_external_trigger_config
    gd32f470vet6_bsp.o(i.AdcPeriphInit) refers to gd32f4xx_adc.o(i.adc_dma_request_after_last_enable) for adc_dma_request_after_last_enable
    gd32f470vet6_bsp.o(i.AdcPeriphInit) refers to gd32f4xx_adc.o(i.adc_dma_mode_enable) for adc_dma_mode_enable
    gd32f470vet6_bsp.o(i.AdcPeriphInit) refers to gd32f4xx_adc.o(i.adc_enable) for adc_enable
    gd32f470vet6_bsp.o(i.AdcPeriphInit) refers to systick.o(i.delay_1ms) for delay_1ms
    gd32f470vet6_bsp.o(i.AdcPeriphInit) refers to gd32f4xx_adc.o(i.adc_calibration_enable) for adc_calibration_enable
    gd32f470vet6_bsp.o(i.AdcPeriphInit) refers to gd32f4xx_adc.o(i.adc_software_trigger_enable) for adc_software_trigger_enable
    gd32f470vet6_bsp.o(i.BtnGetState) refers to gd32f4xx_gpio.o(i.gpio_input_bit_get) for gpio_input_bit_get
    gd32f470vet6_bsp.o(i.BtnPeriphInit) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    gd32f470vet6_bsp.o(i.BtnPeriphInit) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    gd32f470vet6_bsp.o(i.BtnPeriphInit) refers to ebtn.o(i.ebtn_init) for ebtn_init
    gd32f470vet6_bsp.o(i.BtnPeriphInit) refers to ebtn.o(i.ebtn_combo_btn_add_btn) for ebtn_combo_btn_add_btn
    gd32f470vet6_bsp.o(i.BtnPeriphInit) refers to ebtn.o(i.ebtn_set_config) for ebtn_set_config
    gd32f470vet6_bsp.o(i.BtnPeriphInit) refers to ebtn.o(i.ebtn_set_combo_suppress_threshold) for ebtn_set_combo_suppress_threshold
    gd32f470vet6_bsp.o(i.BtnPeriphInit) refers to btn_app.o(i.BtnEventCallback) for BtnEventCallback
    gd32f470vet6_bsp.o(i.BtnPeriphInit) refers to gd32f470vet6_bsp.o(i.BtnGetState) for BtnGetState
    gd32f470vet6_bsp.o(i.BtnPeriphInit) refers to gd32f470vet6_bsp.o(.data) for btns_combo
    gd32f470vet6_bsp.o(i.FlashPeriphInit) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    gd32f470vet6_bsp.o(i.FlashPeriphInit) refers to gd32f4xx_gpio.o(i.gpio_af_set) for gpio_af_set
    gd32f470vet6_bsp.o(i.FlashPeriphInit) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    gd32f470vet6_bsp.o(i.FlashPeriphInit) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    gd32f470vet6_bsp.o(i.FlashPeriphInit) refers to gd32f4xx_spi.o(i.spi_init) for spi_init
    gd32f470vet6_bsp.o(i.FlashPeriphInit) refers to gd25qxx.o(i.spi_flash_init) for spi_flash_init
    gd32f470vet6_bsp.o(i.Gd30PeriphInit) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    gd32f470vet6_bsp.o(i.Gd30PeriphInit) refers to gd32f4xx_gpio.o(i.gpio_af_set) for gpio_af_set
    gd32f470vet6_bsp.o(i.Gd30PeriphInit) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    gd32f470vet6_bsp.o(i.Gd30PeriphInit) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    gd32f470vet6_bsp.o(i.Gd30PeriphInit) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    gd32f470vet6_bsp.o(i.Gd30PeriphInit) refers to gd32f4xx_spi.o(i.spi_init) for spi_init
    gd32f470vet6_bsp.o(i.Gd30PeriphInit) refers to gd32f4xx_spi.o(i.spi_enable) for spi_enable
    gd32f470vet6_bsp.o(i.Gd30PeriphInit) refers to gd30ad3344.o(i.ad3344_reset) for ad3344_reset
    gd32f470vet6_bsp.o(i.GetDays) refers to gd32f470vet6_bsp.o(i.IsLeapYear) for IsLeapYear
    gd32f470vet6_bsp.o(i.GetDays) refers to gd32f470vet6_bsp.o(.constdata) for days_in_month
    gd32f470vet6_bsp.o(i.LedDisp) refers to gd32f4xx_gpio.o(i.gpio_bit_write) for gpio_bit_write
    gd32f470vet6_bsp.o(i.LedDisp) refers to gd32f470vet6_bsp.o(.data) for temp_old
    gd32f470vet6_bsp.o(i.LedPeriphInit) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    gd32f470vet6_bsp.o(i.LedPeriphInit) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    gd32f470vet6_bsp.o(i.LedPeriphInit) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    gd32f470vet6_bsp.o(i.OledDmaInit) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    gd32f470vet6_bsp.o(i.OledDmaInit) refers to gd32f4xx_dma.o(i.dma_deinit) for dma_deinit
    gd32f470vet6_bsp.o(i.OledDmaInit) refers to gd32f4xx_dma.o(i.dma_single_data_para_struct_init) for dma_single_data_para_struct_init
    gd32f470vet6_bsp.o(i.OledDmaInit) refers to gd32f4xx_dma.o(i.dma_single_data_mode_init) for dma_single_data_mode_init
    gd32f470vet6_bsp.o(i.OledDmaInit) refers to gd32f4xx_dma.o(i.dma_circulation_disable) for dma_circulation_disable
    gd32f470vet6_bsp.o(i.OledDmaInit) refers to gd32f4xx_dma.o(i.dma_channel_subperipheral_select) for dma_channel_subperipheral_select
    gd32f470vet6_bsp.o(i.OledDmaInit) refers to gd32f470vet6_bsp.o(.data) for oled_data_buffer
    gd32f470vet6_bsp.o(i.OledDrawStr) refers to vsnprintf.o(.text) for vsnprintf
    gd32f470vet6_bsp.o(i.OledDrawStr) refers to oled.o(i.OLED_ShowStr) for OLED_ShowStr
    gd32f470vet6_bsp.o(i.OledPeriphInit) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    gd32f470vet6_bsp.o(i.OledPeriphInit) refers to gd32f4xx_gpio.o(i.gpio_af_set) for gpio_af_set
    gd32f470vet6_bsp.o(i.OledPeriphInit) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    gd32f470vet6_bsp.o(i.OledPeriphInit) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    gd32f470vet6_bsp.o(i.OledPeriphInit) refers to gd32f4xx_i2c.o(i.i2c_clock_config) for i2c_clock_config
    gd32f470vet6_bsp.o(i.OledPeriphInit) refers to gd32f4xx_i2c.o(i.i2c_mode_addr_config) for i2c_mode_addr_config
    gd32f470vet6_bsp.o(i.OledPeriphInit) refers to gd32f4xx_i2c.o(i.i2c_enable) for i2c_enable
    gd32f470vet6_bsp.o(i.OledPeriphInit) refers to gd32f4xx_i2c.o(i.i2c_ack_config) for i2c_ack_config
    gd32f470vet6_bsp.o(i.OledPeriphInit) refers to gd32f470vet6_bsp.o(i.OledDmaInit) for OledDmaInit
    gd32f470vet6_bsp.o(i.OledPeriphInit) refers to oled.o(i.OLED_Init) for OLED_Init
    gd32f470vet6_bsp.o(i.ReadRtc) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    gd32f470vet6_bsp.o(i.ReadRtc) refers to gd32f4xx_rtc.o(i.rtc_current_time_get) for rtc_current_time_get
    gd32f470vet6_bsp.o(i.ReadRtc) refers to gd32f470vet6_bsp.o(i.BcdToDec) for BcdToDec
    gd32f470vet6_bsp.o(i.Rs485RxMode) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    gd32f470vet6_bsp.o(i.Rs485TxMode) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    gd32f470vet6_bsp.o(i.RtcPeriphInit) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    gd32f470vet6_bsp.o(i.RtcPeriphInit) refers to gd32f4xx_pmu.o(i.pmu_backup_write_enable) for pmu_backup_write_enable
    gd32f470vet6_bsp.o(i.RtcPeriphInit) refers to gd32f470vet6_bsp.o(i.RtcPreConfig) for RtcPreConfig
    gd32f470vet6_bsp.o(i.RtcPeriphInit) refers to gd32f470vet6_bsp.o(i.SetRtc) for SetRtc
    gd32f470vet6_bsp.o(i.RtcPeriphInit) refers to gd32f470vet6_bsp.o(i.Usart0Printf) for Usart0Printf
    gd32f470vet6_bsp.o(i.RtcPeriphInit) refers to gd32f4xx_rcu.o(i.rcu_flag_get) for rcu_flag_get
    gd32f470vet6_bsp.o(i.RtcPeriphInit) refers to gd32f4xx_rcu.o(i.rcu_all_reset_flag_clear) for rcu_all_reset_flag_clear
    gd32f470vet6_bsp.o(i.RtcPeriphInit) refers to gd32f470vet6_bsp.o(.data) for rtcsrc_flag
    gd32f470vet6_bsp.o(i.RtcPeriphInit) refers to gd32f470vet6_bsp.o(.bss) for ucRtc
    gd32f470vet6_bsp.o(i.RtcPreConfig) refers to gd32f4xx_rcu.o(i.rcu_osci_on) for rcu_osci_on
    gd32f470vet6_bsp.o(i.RtcPreConfig) refers to gd32f4xx_rcu.o(i.rcu_osci_stab_wait) for rcu_osci_stab_wait
    gd32f470vet6_bsp.o(i.RtcPreConfig) refers to gd32f4xx_rcu.o(i.rcu_rtc_clock_config) for rcu_rtc_clock_config
    gd32f470vet6_bsp.o(i.RtcPreConfig) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    gd32f470vet6_bsp.o(i.RtcPreConfig) refers to gd32f4xx_rtc.o(i.rtc_register_sync_wait) for rtc_register_sync_wait
    gd32f470vet6_bsp.o(i.RtcPreConfig) refers to gd32f470vet6_bsp.o(.data) for prescaler_s
    gd32f470vet6_bsp.o(i.SetRtc) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    gd32f470vet6_bsp.o(i.SetRtc) refers to gd32f470vet6_bsp.o(i.DecToBcd) for DecToBcd
    gd32f470vet6_bsp.o(i.SetRtc) refers to gd32f470vet6_bsp.o(i.ValidateRtcTime) for ValidateRtcTime
    gd32f470vet6_bsp.o(i.SetRtc) refers to gd32f4xx_rtc.o(i.rtc_init) for rtc_init
    gd32f470vet6_bsp.o(i.SetRtc) refers to gd32f470vet6_bsp.o(.data) for prescaler_a
    gd32f470vet6_bsp.o(i.SysInit) refers to systick.o(i.systick_config) for systick_config
    gd32f470vet6_bsp.o(i.SysInit) refers to gd32f470vet6_bsp.o(i.LedPeriphInit) for LedPeriphInit
    gd32f470vet6_bsp.o(i.SysInit) refers to gd32f470vet6_bsp.o(i.BtnPeriphInit) for BtnPeriphInit
    gd32f470vet6_bsp.o(i.SysInit) refers to gd32f470vet6_bsp.o(i.AdcPeriphInit) for AdcPeriphInit
    gd32f470vet6_bsp.o(i.SysInit) refers to gd32f470vet6_bsp.o(i.Usart0PeriphInit) for Usart0PeriphInit
    gd32f470vet6_bsp.o(i.SysInit) refers to gd32f470vet6_bsp.o(i.Usart1PeriphInit) for Usart1PeriphInit
    gd32f470vet6_bsp.o(i.SysInit) refers to gd32f470vet6_bsp.o(i.OledPeriphInit) for OledPeriphInit
    gd32f470vet6_bsp.o(i.SysInit) refers to gd32f470vet6_bsp.o(i.FlashPeriphInit) for FlashPeriphInit
    gd32f470vet6_bsp.o(i.SysInit) refers to gd32f470vet6_bsp.o(i.Gd30PeriphInit) for Gd30PeriphInit
    gd32f470vet6_bsp.o(i.SysInit) refers to gd32f470vet6_bsp.o(i.RtcPeriphInit) for RtcPeriphInit
    gd32f470vet6_bsp.o(i.SysInit) refers to gd32f470vet6_bsp.o(i.TfPeriphInit) for TfPeriphInit
    gd32f470vet6_bsp.o(i.TfPeriphInit) refers to gd32f4xx_misc.o(i.nvic_irq_enable) for nvic_irq_enable
    gd32f470vet6_bsp.o(i.TfPeriphInit) refers to diskio.o(i.disk_initialize) for disk_initialize
    gd32f470vet6_bsp.o(i.TfPeriphInit) refers to gd32f470vet6_bsp.o(i.Usart1Printf) for Usart1Printf
    gd32f470vet6_bsp.o(i.TfPeriphInit) refers to ff.o(i.f_mount) for f_mount
    gd32f470vet6_bsp.o(i.TfPeriphInit) refers to gd32f470vet6_bsp.o(.bss) for fs
    gd32f470vet6_bsp.o(i.Usart0DmaInit) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    gd32f470vet6_bsp.o(i.Usart0DmaInit) refers to gd32f4xx_dma.o(i.dma_deinit) for dma_deinit
    gd32f470vet6_bsp.o(i.Usart0DmaInit) refers to gd32f4xx_dma.o(i.dma_single_data_mode_init) for dma_single_data_mode_init
    gd32f470vet6_bsp.o(i.Usart0DmaInit) refers to gd32f4xx_dma.o(i.dma_circulation_disable) for dma_circulation_disable
    gd32f470vet6_bsp.o(i.Usart0DmaInit) refers to gd32f4xx_dma.o(i.dma_channel_subperipheral_select) for dma_channel_subperipheral_select
    gd32f470vet6_bsp.o(i.Usart0DmaInit) refers to gd32f4xx_dma.o(i.dma_channel_enable) for dma_channel_enable
    gd32f470vet6_bsp.o(i.Usart0DmaInit) refers to gd32f470vet6_bsp.o(.bss) for usart0_rx_buffer_dma
    gd32f470vet6_bsp.o(i.Usart0PeriphInit) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    gd32f470vet6_bsp.o(i.Usart0PeriphInit) refers to gd32f4xx_gpio.o(i.gpio_af_set) for gpio_af_set
    gd32f470vet6_bsp.o(i.Usart0PeriphInit) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    gd32f470vet6_bsp.o(i.Usart0PeriphInit) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    gd32f470vet6_bsp.o(i.Usart0PeriphInit) refers to gd32f4xx_usart.o(i.usart_deinit) for usart_deinit
    gd32f470vet6_bsp.o(i.Usart0PeriphInit) refers to gd32f4xx_usart.o(i.usart_baudrate_set) for usart_baudrate_set
    gd32f470vet6_bsp.o(i.Usart0PeriphInit) refers to gd32f4xx_usart.o(i.usart_transmit_config) for usart_transmit_config
    gd32f470vet6_bsp.o(i.Usart0PeriphInit) refers to gd32f4xx_usart.o(i.usart_receive_config) for usart_receive_config
    gd32f470vet6_bsp.o(i.Usart0PeriphInit) refers to gd32f4xx_usart.o(i.usart_dma_receive_config) for usart_dma_receive_config
    gd32f470vet6_bsp.o(i.Usart0PeriphInit) refers to gd32f4xx_usart.o(i.usart_enable) for usart_enable
    gd32f470vet6_bsp.o(i.Usart0PeriphInit) refers to gd32f470vet6_bsp.o(i.Usart0DmaInit) for Usart0DmaInit
    gd32f470vet6_bsp.o(i.Usart0PeriphInit) refers to gd32f4xx_misc.o(i.nvic_irq_enable) for nvic_irq_enable
    gd32f470vet6_bsp.o(i.Usart0PeriphInit) refers to gd32f4xx_usart.o(i.usart_interrupt_enable) for usart_interrupt_enable
    gd32f470vet6_bsp.o(i.Usart0Printf) refers to vsnprintf.o(.text) for vsnprintf
    gd32f470vet6_bsp.o(i.Usart0Printf) refers to gd32f4xx_usart.o(i.usart_data_transmit) for usart_data_transmit
    gd32f470vet6_bsp.o(i.Usart0Printf) refers to gd32f4xx_usart.o(i.usart_flag_get) for usart_flag_get
    gd32f470vet6_bsp.o(i.Usart0Printf) refers to gd32f470vet6_bsp.o(.data) for uwTick
    gd32f470vet6_bsp.o(i.Usart1DmaInit) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    gd32f470vet6_bsp.o(i.Usart1DmaInit) refers to gd32f4xx_dma.o(i.dma_deinit) for dma_deinit
    gd32f470vet6_bsp.o(i.Usart1DmaInit) refers to gd32f4xx_dma.o(i.dma_single_data_mode_init) for dma_single_data_mode_init
    gd32f470vet6_bsp.o(i.Usart1DmaInit) refers to gd32f4xx_dma.o(i.dma_circulation_disable) for dma_circulation_disable
    gd32f470vet6_bsp.o(i.Usart1DmaInit) refers to gd32f4xx_dma.o(i.dma_channel_subperipheral_select) for dma_channel_subperipheral_select
    gd32f470vet6_bsp.o(i.Usart1DmaInit) refers to gd32f4xx_dma.o(i.dma_channel_enable) for dma_channel_enable
    gd32f470vet6_bsp.o(i.Usart1DmaInit) refers to gd32f470vet6_bsp.o(.bss) for usart1_rx_buffer_dma
    gd32f470vet6_bsp.o(i.Usart1PeriphInit) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    gd32f470vet6_bsp.o(i.Usart1PeriphInit) refers to gd32f4xx_gpio.o(i.gpio_af_set) for gpio_af_set
    gd32f470vet6_bsp.o(i.Usart1PeriphInit) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    gd32f470vet6_bsp.o(i.Usart1PeriphInit) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    gd32f470vet6_bsp.o(i.Usart1PeriphInit) refers to gd32f4xx_usart.o(i.usart_deinit) for usart_deinit
    gd32f470vet6_bsp.o(i.Usart1PeriphInit) refers to gd32f4xx_usart.o(i.usart_baudrate_set) for usart_baudrate_set
    gd32f470vet6_bsp.o(i.Usart1PeriphInit) refers to gd32f4xx_usart.o(i.usart_transmit_config) for usart_transmit_config
    gd32f470vet6_bsp.o(i.Usart1PeriphInit) refers to gd32f4xx_usart.o(i.usart_receive_config) for usart_receive_config
    gd32f470vet6_bsp.o(i.Usart1PeriphInit) refers to gd32f4xx_usart.o(i.usart_dma_receive_config) for usart_dma_receive_config
    gd32f470vet6_bsp.o(i.Usart1PeriphInit) refers to gd32f4xx_usart.o(i.usart_enable) for usart_enable
    gd32f470vet6_bsp.o(i.Usart1PeriphInit) refers to gd32f470vet6_bsp.o(i.Usart1DmaInit) for Usart1DmaInit
    gd32f470vet6_bsp.o(i.Usart1PeriphInit) refers to gd32f4xx_misc.o(i.nvic_irq_enable) for nvic_irq_enable
    gd32f470vet6_bsp.o(i.Usart1PeriphInit) refers to gd32f4xx_usart.o(i.usart_interrupt_enable) for usart_interrupt_enable
    gd32f470vet6_bsp.o(i.Usart1PeriphInit) refers to gd32f470vet6_bsp.o(i.Rs485RxMode) for Rs485RxMode
    gd32f470vet6_bsp.o(i.Usart1Printf) refers to vsnprintf.o(.text) for vsnprintf
    gd32f470vet6_bsp.o(i.Usart1Printf) refers to gd32f470vet6_bsp.o(i.Rs485TxMode) for Rs485TxMode
    gd32f470vet6_bsp.o(i.Usart1Printf) refers to gd32f4xx_usart.o(i.usart_data_transmit) for usart_data_transmit
    gd32f470vet6_bsp.o(i.Usart1Printf) refers to gd32f4xx_usart.o(i.usart_flag_get) for usart_flag_get
    gd32f470vet6_bsp.o(i.Usart1Printf) refers to gd32f470vet6_bsp.o(i.Rs485RxMode) for Rs485RxMode
    gd32f470vet6_bsp.o(i.Usart1Printf) refers to gd32f470vet6_bsp.o(.data) for uwTick
    gd32f470vet6_bsp.o(i.ValidateRtcTime) refers to gd32f470vet6_bsp.o(i.GetDays) for GetDays
    gd32f470vet6_bsp.o(.data) refers to usart_app.o(i.Usart0Task) for Usart0Task
    gd32f470vet6_bsp.o(.data) refers to usart_app.o(i.Usart1Task) for Usart1Task
    gd32f470vet6_bsp.o(.data) refers to btn_app.o(i.BtnTask) for BtnTask
    gd32f470vet6_bsp.o(.data) refers to adc_app.o(i.AdcTask) for AdcTask
    gd32f470vet6_bsp.o(.data) refers to adc_app.o(i.Gd30Task) for Gd30Task
    gd32f470vet6_bsp.o(.data) refers to adc_app.o(i.SampleTask) for SampleTask
    gd32f470vet6_bsp.o(.data) refers to led_app.o(i.LedTask) for LedTask
    gd32f470vet6_bsp.o(.data) refers to rtc_app.o(i.RtcTask) for RtcTask
    gd32f470vet6_bsp.o(.data) refers to oled_app.o(i.OledTask) for OledTask
    gd32f470vet6_bsp.o(.data) refers to gd32f470vet6_bsp.o(.constdata) for defaul_ebtn_param
    main.o(i.main) refers to gd32f470vet6_bsp.o(i.SysInit) for SysInit
    main.o(i.main) refers to flash_app.o(i.Flash_ReadDeviceId) for Flash_ReadDeviceId
    main.o(i.main) refers to scheduler.o(i.TaskExeution) for TaskExeution
    main.o(.data) refers to main.o(.conststring) for .conststring
    scheduler.o(i.TaskExeution) refers to gd32f470vet6_bsp.o(.data) for schedul_task
    systick.o(i.delay_1ms) refers to systick.o(.data) for delay
    systick.o(i.delay_decrement) refers to systick.o(.data) for delay
    systick.o(i.systick_config) refers to systick.o(i.NVIC_SetPriority) for NVIC_SetPriority
    systick.o(i.systick_config) refers to system_gd32f4xx.o(.data) for SystemCoreClock
    adc_app.o(i.AdcTask) refers to gd32f470vet6_bsp.o(.bss) for adc0_value
    adc_app.o(i.AdcTask) refers to gd32f470vet6_bsp.o(.data) for adc0_voltage
    adc_app.o(i.Gd30Task) refers to gd30ad3344.o(i.ad3344_init) for ad3344_init
    adc_app.o(i.Gd30Task) refers to gd30ad3344.o(i.ad3344_read_data16) for ad3344_read_data16
    adc_app.o(i.Gd30Task) refers to adc_app.o(.data) for gd30_state
    adc_app.o(i.Gd30Task) refers to main.o(.data) for gd30_channels
    adc_app.o(i.SampleProc) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    adc_app.o(i.SampleProc) refers to gd32f470vet6_bsp.o(i.Usart1Printf) for Usart1Printf
    adc_app.o(i.SampleProc) refers to gd32f470vet6_bsp.o(.bss) for ucRtc
    adc_app.o(i.SampleProc) refers to main.o(.data) for sample_status
    adc_app.o(i.SampleProc) refers to adc_app.o(.conststring) for .conststring
    adc_app.o(i.SampleTask) refers to adc_app.o(i.SampleProc) for SampleProc
    adc_app.o(i.SampleTask) refers to main.o(.data) for sample_status
    adc_app.o(i.SampleTask) refers to gd32f470vet6_bsp.o(.bss) for ucRtc
    led_app.o(i.LedTask) refers to gd32f470vet6_bsp.o(i.LedDisp) for LedDisp
    led_app.o(i.LedTask) refers to gd32f470vet6_bsp.o(.data) for ucLed
    command_proc.o(i.CmdGetData) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    command_proc.o(i.CmdGetData) refers to gd32f470vet6_bsp.o(i.Usart1Printf) for Usart1Printf
    command_proc.o(i.CmdGetData) refers to main.o(.data) for gd30_channels
    command_proc.o(i.CmdProc) refers to strcmpv7m.o(.text) for strcmp
    command_proc.o(i.CmdProc) refers to command_proc.o(i.Cmd_GetDeviceId) for Cmd_GetDeviceId
    command_proc.o(i.CmdProc) refers to command_proc.o(i.Cmd_GetRtc) for Cmd_GetRtc
    command_proc.o(i.CmdProc) refers to strncmp.o(.text) for strncmp
    command_proc.o(i.CmdProc) refers to command_proc.o(i.Cmd_SetRtc) for Cmd_SetRtc
    command_proc.o(i.CmdProc) refers to command_proc.o(i.Cmd_SetRatio) for Cmd_SetRatio
    command_proc.o(i.CmdProc) refers to command_proc.o(i.Cmd_GetRatio) for Cmd_GetRatio
    command_proc.o(i.CmdProc) refers to command_proc.o(i.CmdGetData) for CmdGetData
    command_proc.o(i.CmdProc) refers to command_proc.o(i.CmdStartSample) for CmdStartSample
    command_proc.o(i.CmdProc) refers to command_proc.o(i.CmdStopSample) for CmdStopSample
    command_proc.o(i.CmdProc) refers to command_proc.o(i.Cmd_SetLimit) for Cmd_SetLimit
    command_proc.o(i.CmdStartSample) refers to adc_app.o(i.SampleProc) for SampleProc
    command_proc.o(i.CmdStartSample) refers to main.o(.data) for sample_status
    command_proc.o(i.CmdStopSample) refers to gd32f470vet6_bsp.o(i.Usart1Printf) for Usart1Printf
    command_proc.o(i.CmdStopSample) refers to main.o(.data) for sample_status
    command_proc.o(i.Cmd_GetDeviceId) refers to gd32f470vet6_bsp.o(i.Usart1Printf) for Usart1Printf
    command_proc.o(i.Cmd_GetDeviceId) refers to main.o(.bss) for device_id
    command_proc.o(i.Cmd_GetRatio) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    command_proc.o(i.Cmd_GetRatio) refers to gd32f470vet6_bsp.o(i.Usart1Printf) for Usart1Printf
    command_proc.o(i.Cmd_GetRatio) refers to main.o(.data) for config_data
    command_proc.o(i.Cmd_GetRtc) refers to gd32f470vet6_bsp.o(i.Usart1Printf) for Usart1Printf
    command_proc.o(i.Cmd_GetRtc) refers to gd32f470vet6_bsp.o(.bss) for ucRtc
    command_proc.o(i.Cmd_SetLimit) refers to scanf1.o(x$fpl$scanf1) for _scanf_real
    command_proc.o(i.Cmd_SetLimit) refers to __0sscanf.o(.text) for __0sscanf
    command_proc.o(i.Cmd_SetLimit) refers to strlen.o(.text) for strlen
    command_proc.o(i.Cmd_SetLimit) refers to tf_app.o(i.TF_WriteLimit) for TF_WriteLimit
    command_proc.o(i.Cmd_SetLimit) refers to gd32f470vet6_bsp.o(i.Usart1Printf) for Usart1Printf
    command_proc.o(i.Cmd_SetLimit) refers to main.o(.data) for config_data
    command_proc.o(i.Cmd_SetRatio) refers to scanf1.o(x$fpl$scanf1) for _scanf_real
    command_proc.o(i.Cmd_SetRatio) refers to __0sscanf.o(.text) for __0sscanf
    command_proc.o(i.Cmd_SetRatio) refers to strlen.o(.text) for strlen
    command_proc.o(i.Cmd_SetRatio) refers to tf_app.o(i.TF_WriteRatio) for TF_WriteRatio
    command_proc.o(i.Cmd_SetRatio) refers to gd32f470vet6_bsp.o(i.Usart1Printf) for Usart1Printf
    command_proc.o(i.Cmd_SetRatio) refers to main.o(.data) for config_data
    command_proc.o(i.Cmd_SetRtc) refers to _scanf_int.o(.text) for _scanf_int
    command_proc.o(i.Cmd_SetRtc) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    command_proc.o(i.Cmd_SetRtc) refers to __0sscanf.o(.text) for __0sscanf
    command_proc.o(i.Cmd_SetRtc) refers to strlen.o(.text) for strlen
    command_proc.o(i.Cmd_SetRtc) refers to gd32f470vet6_bsp.o(i.SetRtc) for SetRtc
    command_proc.o(i.Cmd_SetRtc) refers to gd32f470vet6_bsp.o(i.Usart1Printf) for Usart1Printf
    flash_app.o(i.Flash_ReadDeviceId) refers to gd25qxx.o(i.spi_flash_buffer_read) for spi_flash_buffer_read
    flash_app.o(i.Flash_ReadDeviceId) refers to main.o(.bss) for device_id
    btn_app.o(i.BtnEventCallback) refers to gd32f470vet6_bsp.o(.data) for ucLed
    btn_app.o(i.BtnTask) refers to ebtn.o(i.ebtn_process) for ebtn_process
    btn_app.o(i.BtnTask) refers to gd32f470vet6_bsp.o(.data) for uwTick
    oled_app.o(i.OledTask) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    oled_app.o(i.OledTask) refers to gd32f470vet6_bsp.o(i.OledDrawStr) for OledDrawStr
    oled_app.o(i.OledTask) refers to gd32f470vet6_bsp.o(.data) for adc0_voltage
    rtc_app.o(i.RtcTask) refers to gd32f470vet6_bsp.o(i.ReadRtc) for ReadRtc
    rtc_app.o(i.RtcTask) refers to gd32f470vet6_bsp.o(.bss) for ucRtc
    tf_app.o(i.TF_WriteConfig) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    tf_app.o(i.TF_WriteConfig) refers to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    tf_app.o(i.TF_WriteConfig) refers to printf1.o(x$fpl$printf1) for _printf_fp_dec
    tf_app.o(i.TF_WriteConfig) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    tf_app.o(i.TF_WriteConfig) refers to ff.o(i.f_open) for f_open
    tf_app.o(i.TF_WriteConfig) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    tf_app.o(i.TF_WriteConfig) refers to __2sprintf.o(.text) for __2sprintf
    tf_app.o(i.TF_WriteConfig) refers to strlen.o(.text) for strlen
    tf_app.o(i.TF_WriteConfig) refers to ff.o(i.f_write) for f_write
    tf_app.o(i.TF_WriteConfig) refers to ff.o(i.f_sync) for f_sync
    tf_app.o(i.TF_WriteConfig) refers to tf_app.o(.conststring) for .conststring
    tf_app.o(i.TF_WriteLimit) refers to scanf1.o(x$fpl$scanf1) for _scanf_real
    tf_app.o(i.TF_WriteLimit) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    tf_app.o(i.TF_WriteLimit) refers to ff.o(i.f_open) for f_open
    tf_app.o(i.TF_WriteLimit) refers to gd32f470vet6_bsp.o(i.Usart1Printf) for Usart1Printf
    tf_app.o(i.TF_WriteLimit) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    tf_app.o(i.TF_WriteLimit) refers to tf_app.o(i.TF_WriteConfig) for TF_WriteConfig
    tf_app.o(i.TF_WriteLimit) refers to ff.o(i.f_read) for f_read
    tf_app.o(i.TF_WriteLimit) refers to ff.o(i.f_close) for f_close
    tf_app.o(i.TF_WriteLimit) refers to __0sscanf.o(.text) for __0sscanf
    tf_app.o(i.TF_WriteLimit) refers to strlen.o(.text) for strlen
    tf_app.o(i.TF_WriteLimit) refers to tf_app.o(.constdata) for .constdata
    tf_app.o(i.TF_WriteLimit) refers to tf_app.o(.conststring) for .conststring
    tf_app.o(i.TF_WriteRatio) refers to scanf1.o(x$fpl$scanf1) for _scanf_real
    tf_app.o(i.TF_WriteRatio) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    tf_app.o(i.TF_WriteRatio) refers to ff.o(i.f_open) for f_open
    tf_app.o(i.TF_WriteRatio) refers to ff.o(i.f_read) for f_read
    tf_app.o(i.TF_WriteRatio) refers to ff.o(i.f_close) for f_close
    tf_app.o(i.TF_WriteRatio) refers to __0sscanf.o(.text) for __0sscanf
    tf_app.o(i.TF_WriteRatio) refers to strlen.o(.text) for strlen
    tf_app.o(i.TF_WriteRatio) refers to tf_app.o(i.TF_WriteConfig) for TF_WriteConfig
    tf_app.o(i.TF_WriteRatio) refers to tf_app.o(.conststring) for .conststring
    tf_app.o(i.TfCardTest) refers to diskio.o(i.disk_initialize) for disk_initialize
    tf_app.o(i.TfCardTest) refers to tf_app.o(i.card_info_get) for card_info_get
    tf_app.o(i.TfCardTest) refers to gd32f470vet6_bsp.o(i.Usart0Printf) for Usart0Printf
    tf_app.o(i.TfCardTest) refers to ff.o(i.f_mount) for f_mount
    tf_app.o(i.TfCardTest) refers to ff.o(i.f_open) for f_open
    tf_app.o(i.TfCardTest) refers to __2sprintf.o(.text) for __2sprintf
    tf_app.o(i.TfCardTest) refers to ff.o(i.f_write) for f_write
    tf_app.o(i.TfCardTest) refers to ff.o(i.f_close) for f_close
    tf_app.o(i.TfCardTest) refers to ff.o(i.f_read) for f_read
    tf_app.o(i.TfCardTest) refers to tf_app.o(i.memory_compare) for memory_compare
    tf_app.o(i.TfCardTest) refers to gd32f470vet6_bsp.o(.bss) for fs
    tf_app.o(i.TfCardTest) refers to tf_app.o(.bss) for fdst
    tf_app.o(i.TfCardTest) refers to tf_app.o(.data) for result
    tf_app.o(i.card_info_get) refers to sdio_sdcard.o(i.sd_card_information_get) for sd_card_information_get
    tf_app.o(i.card_info_get) refers to gd32f470vet6_bsp.o(i.Usart0Printf) for Usart0Printf
    tf_app.o(i.card_info_get) refers to sdio_sdcard.o(i.sd_card_capacity_get) for sd_card_capacity_get
    tf_app.o(i.card_info_get) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    usart_app.o(i.Usart0Task) refers to gd32f470vet6_bsp.o(i.Usart0Printf) for Usart0Printf
    usart_app.o(i.Usart0Task) refers to rt_memclr.o(.text) for __aeabi_memclr
    usart_app.o(i.Usart0Task) refers to gd32f470vet6_bsp.o(.data) for usart0_rx_flag
    usart_app.o(i.Usart0Task) refers to gd32f470vet6_bsp.o(.bss) for usart0_rx_buffer_proc
    usart_app.o(i.Usart1Task) refers to command_proc.o(i.CmdProc) for CmdProc
    usart_app.o(i.Usart1Task) refers to rt_memclr.o(.text) for __aeabi_memclr
    usart_app.o(i.Usart1Task) refers to gd32f470vet6_bsp.o(.data) for usart1_rx_flag
    usart_app.o(i.Usart1Task) refers to gd32f470vet6_bsp.o(.bss) for usart1_rx_buffer_proc
    ebtn.o(i.bit_array_cmp) refers to memcmp.o(.text) for memcmp
    ebtn.o(i.ebtn_combo_btn_add_btn) refers to ebtn.o(i.ebtn_get_btn_index_by_key_id) for ebtn_get_btn_index_by_key_id
    ebtn.o(i.ebtn_combo_btn_add_btn) refers to ebtn.o(i.ebtn_combo_btn_add_btn_by_idx) for ebtn_combo_btn_add_btn_by_idx
    ebtn.o(i.ebtn_combo_btn_remove_btn) refers to ebtn.o(i.ebtn_get_btn_index_by_key_id) for ebtn_get_btn_index_by_key_id
    ebtn.o(i.ebtn_combo_btn_remove_btn) refers to ebtn.o(i.ebtn_combo_btn_remove_btn_by_idx) for ebtn_combo_btn_remove_btn_by_idx
    ebtn.o(i.ebtn_combo_register) refers to ebtn.o(.bss) for ebtn_default
    ebtn.o(i.ebtn_get_btn_by_key_id) refers to ebtn.o(.bss) for ebtn_default
    ebtn.o(i.ebtn_get_btn_index_by_btn) refers to ebtn.o(i.ebtn_get_btn_index_by_key_id) for ebtn_get_btn_index_by_key_id
    ebtn.o(i.ebtn_get_btn_index_by_btn_dyn) refers to ebtn.o(i.ebtn_get_btn_index_by_key_id) for ebtn_get_btn_index_by_key_id
    ebtn.o(i.ebtn_get_btn_index_by_key_id) refers to ebtn.o(.bss) for ebtn_default
    ebtn.o(i.ebtn_get_config) refers to ebtn.o(.bss) for ebtn_default
    ebtn.o(i.ebtn_get_current_state) refers to ebtn.o(i.bit_array_assign) for bit_array_assign
    ebtn.o(i.ebtn_get_current_state) refers to ebtn.o(.bss) for ebtn_default
    ebtn.o(i.ebtn_get_total_btn_cnt) refers to ebtn.o(.bss) for ebtn_default
    ebtn.o(i.ebtn_init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    ebtn.o(i.ebtn_init) refers to ebtn.o(.bss) for ebtn_default
    ebtn.o(i.ebtn_is_in_process) refers to ebtn.o(i.ebtn_is_btn_in_process) for ebtn_is_btn_in_process
    ebtn.o(i.ebtn_is_in_process) refers to ebtn.o(.bss) for ebtn_default
    ebtn.o(i.ebtn_process) refers to ebtn.o(i.ebtn_get_current_state) for ebtn_get_current_state
    ebtn.o(i.ebtn_process) refers to ebtn.o(i.ebtn_process_with_curr_state) for ebtn_process_with_curr_state
    ebtn.o(i.ebtn_process_btn) refers to ebtn.o(i.bit_array_get) for bit_array_get
    ebtn.o(i.ebtn_process_btn) refers to ebtn.o(i.prv_process_btn) for prv_process_btn
    ebtn.o(i.ebtn_process_btn_combo) refers to ebtn.o(i.bit_array_num_bits_set) for bit_array_num_bits_set
    ebtn.o(i.ebtn_process_btn_combo) refers to ebtn.o(i.bit_array_and) for bit_array_and
    ebtn.o(i.ebtn_process_btn_combo) refers to ebtn.o(i.bit_array_cmp) for bit_array_cmp
    ebtn.o(i.ebtn_process_btn_combo) refers to ebtn.o(i.prv_process_btn) for prv_process_btn
    ebtn.o(i.ebtn_process_with_curr_state) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    ebtn.o(i.ebtn_process_with_curr_state) refers to ebtn.o(i.bit_array_and) for bit_array_and
    ebtn.o(i.ebtn_process_with_curr_state) refers to ebtn.o(i.bit_array_cmp) for bit_array_cmp
    ebtn.o(i.ebtn_process_with_curr_state) refers to ebtn.o(i.bit_array_or) for bit_array_or
    ebtn.o(i.ebtn_process_with_curr_state) refers to ebtn.o(i.bit_array_get) for bit_array_get
    ebtn.o(i.ebtn_process_with_curr_state) refers to ebtn.o(i.ebtn_process_btn) for ebtn_process_btn
    ebtn.o(i.ebtn_process_with_curr_state) refers to ebtn.o(i.ebtn_process_btn_combo) for ebtn_process_btn_combo
    ebtn.o(i.ebtn_process_with_curr_state) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    ebtn.o(i.ebtn_process_with_curr_state) refers to ebtn.o(.bss) for ebtn_default
    ebtn.o(i.ebtn_register) refers to ebtn.o(i.ebtn_get_total_btn_cnt) for ebtn_get_total_btn_cnt
    ebtn.o(i.ebtn_register) refers to ebtn.o(.bss) for ebtn_default
    ebtn.o(i.ebtn_set_combo_suppress_threshold) refers to ebtn.o(.bss) for ebtn_default
    ebtn.o(i.ebtn_set_config) refers to ebtn.o(.bss) for ebtn_default
    ebtn.o(i.prv_get_combo_btn_by_key_id) refers to ebtn.o(.bss) for ebtn_default
    ebtn.o(i.prv_process_btn) refers to ebtn.o(i.ebtn_timer_sub) for ebtn_timer_sub
    ebtn.o(i.prv_process_btn) refers to ebtn.o(i.prv_get_combo_btn_by_key_id) for prv_get_combo_btn_by_key_id
    ebtn.o(i.prv_process_btn) refers to ebtn.o(i.bit_array_get) for bit_array_get
    ebtn.o(i.prv_process_btn) refers to ebtn.o(.bss) for ebtn_default
    oled.o(i.I2C_Bus_Reset) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    oled.o(i.I2C_Bus_Reset) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    oled.o(i.I2C_Bus_Reset) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    oled.o(i.I2C_Bus_Reset) refers to systick.o(i.delay_1ms) for delay_1ms
    oled.o(i.I2C_Bus_Reset) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    oled.o(i.I2C_Bus_Reset) refers to gd32f4xx_i2c.o(i.i2c_deinit) for i2c_deinit
    oled.o(i.I2C_Bus_Reset) refers to gd32f4xx_i2c.o(i.i2c_clock_config) for i2c_clock_config
    oled.o(i.I2C_Bus_Reset) refers to gd32f4xx_i2c.o(i.i2c_mode_addr_config) for i2c_mode_addr_config
    oled.o(i.I2C_Bus_Reset) refers to gd32f4xx_i2c.o(i.i2c_enable) for i2c_enable
    oled.o(i.I2C_Bus_Reset) refers to gd32f4xx_i2c.o(i.i2c_ack_config) for i2c_ack_config
    oled.o(i.OLED_Allfill) refers to aeabi_memset.o(.text) for __aeabi_memset
    oled.o(i.OLED_Allfill) refers to oled.o(i.OLED_Write_cmd) for OLED_Write_cmd
    oled.o(i.OLED_Allfill) refers to oled.o(i.OLED_Write_data_batch) for OLED_Write_data_batch
    oled.o(i.OLED_Clear) refers to oled.o(i.OLED_Flush_buffer) for OLED_Flush_buffer
    oled.o(i.OLED_Clear) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    oled.o(i.OLED_Clear) refers to oled.o(i.OLED_Write_cmd) for OLED_Write_cmd
    oled.o(i.OLED_Clear) refers to oled.o(i.OLED_Write_data_batch) for OLED_Write_data_batch
    oled.o(i.OLED_Display_Off) refers to oled.o(i.OLED_Write_cmd) for OLED_Write_cmd
    oled.o(i.OLED_Display_On) refers to oled.o(i.OLED_Write_cmd) for OLED_Write_cmd
    oled.o(i.OLED_Flush_buffer) refers to oled.o(i.OLED_Write_data_batch) for OLED_Write_data_batch
    oled.o(i.OLED_Flush_buffer) refers to gd32f470vet6_bsp.o(.data) for oled_buffer_index
    oled.o(i.OLED_Init) refers to systick.o(i.delay_1ms) for delay_1ms
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_Write_cmd) for OLED_Write_cmd
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_Clear) for OLED_Clear
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_Set_Position) for OLED_Set_Position
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_Flush_buffer) for OLED_Flush_buffer
    oled.o(i.OLED_Init) refers to oled.o(.data) for initcmd1
    oled.o(i.OLED_Set_Position) refers to oled.o(i.OLED_Flush_buffer) for OLED_Flush_buffer
    oled.o(i.OLED_Set_Position) refers to oled.o(i.OLED_Write_cmd) for OLED_Write_cmd
    oled.o(i.OLED_ShowChar) refers to oled.o(i.OLED_Set_Position) for OLED_Set_Position
    oled.o(i.OLED_ShowChar) refers to oled.o(i.OLED_Write_data) for OLED_Write_data
    oled.o(i.OLED_ShowChar) refers to oled.o(i.OLED_Flush_buffer) for OLED_Flush_buffer
    oled.o(i.OLED_ShowChar) refers to oled.o(.constdata) for F8X16
    oled.o(i.OLED_ShowChar) refers to gd32f470vet6_bsp.o(.data) for oled_buffer_threshold
    oled.o(i.OLED_ShowFloat) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowHanzi) refers to oled.o(i.OLED_Set_Position) for OLED_Set_Position
    oled.o(i.OLED_ShowHanzi) refers to oled.o(i.OLED_Write_data) for OLED_Write_data
    oled.o(i.OLED_ShowHanzi) refers to oled.o(.constdata) for Hzk
    oled.o(i.OLED_ShowHzbig) refers to oled.o(i.OLED_Set_Position) for OLED_Set_Position
    oled.o(i.OLED_ShowHzbig) refers to oled.o(i.OLED_Write_data) for OLED_Write_data
    oled.o(i.OLED_ShowHzbig) refers to oled.o(.constdata) for Hzb
    oled.o(i.OLED_ShowNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowPic) refers to oled.o(i.OLED_Set_Position) for OLED_Set_Position
    oled.o(i.OLED_ShowPic) refers to oled.o(i.OLED_Write_data_batch) for OLED_Write_data_batch
    oled.o(i.OLED_ShowStr) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowStr) refers to oled.o(i.OLED_Flush_buffer) for OLED_Flush_buffer
    oled.o(i.OLED_Write_cmd) refers to gd32f4xx_i2c.o(i.i2c_flag_get) for i2c_flag_get
    oled.o(i.OLED_Write_cmd) refers to oled.o(i.I2C_Bus_Reset) for I2C_Bus_Reset
    oled.o(i.OLED_Write_cmd) refers to systick.o(i.delay_1ms) for delay_1ms
    oled.o(i.OLED_Write_cmd) refers to gd32f4xx_i2c.o(i.i2c_start_on_bus) for i2c_start_on_bus
    oled.o(i.OLED_Write_cmd) refers to gd32f4xx_i2c.o(i.i2c_master_addressing) for i2c_master_addressing
    oled.o(i.OLED_Write_cmd) refers to gd32f4xx_i2c.o(i.i2c_flag_clear) for i2c_flag_clear
    oled.o(i.OLED_Write_cmd) refers to gd32f4xx_dma.o(i.dma_memory_address_config) for dma_memory_address_config
    oled.o(i.OLED_Write_cmd) refers to gd32f4xx_dma.o(i.dma_transfer_number_config) for dma_transfer_number_config
    oled.o(i.OLED_Write_cmd) refers to gd32f4xx_i2c.o(i.i2c_dma_config) for i2c_dma_config
    oled.o(i.OLED_Write_cmd) refers to gd32f4xx_dma.o(i.dma_channel_enable) for dma_channel_enable
    oled.o(i.OLED_Write_cmd) refers to gd32f4xx_dma.o(i.dma_flag_get) for dma_flag_get
    oled.o(i.OLED_Write_cmd) refers to gd32f4xx_dma.o(i.dma_flag_clear) for dma_flag_clear
    oled.o(i.OLED_Write_cmd) refers to gd32f4xx_dma.o(i.dma_channel_disable) for dma_channel_disable
    oled.o(i.OLED_Write_cmd) refers to gd32f4xx_i2c.o(i.i2c_stop_on_bus) for i2c_stop_on_bus
    oled.o(i.OLED_Write_cmd) refers to gd32f470vet6_bsp.o(.data) for oled_cmd_buffer
    oled.o(i.OLED_Write_data) refers to oled.o(i.OLED_Write_data_buffered) for OLED_Write_data_buffered
    oled.o(i.OLED_Write_data_batch) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    oled.o(i.OLED_Write_data_batch) refers to gd32f4xx_i2c.o(i.i2c_flag_get) for i2c_flag_get
    oled.o(i.OLED_Write_data_batch) refers to oled.o(i.I2C_Bus_Reset) for I2C_Bus_Reset
    oled.o(i.OLED_Write_data_batch) refers to systick.o(i.delay_1ms) for delay_1ms
    oled.o(i.OLED_Write_data_batch) refers to gd32f4xx_i2c.o(i.i2c_start_on_bus) for i2c_start_on_bus
    oled.o(i.OLED_Write_data_batch) refers to gd32f4xx_i2c.o(i.i2c_master_addressing) for i2c_master_addressing
    oled.o(i.OLED_Write_data_batch) refers to gd32f4xx_i2c.o(i.i2c_flag_clear) for i2c_flag_clear
    oled.o(i.OLED_Write_data_batch) refers to gd32f4xx_dma.o(i.dma_memory_address_config) for dma_memory_address_config
    oled.o(i.OLED_Write_data_batch) refers to gd32f4xx_dma.o(i.dma_transfer_number_config) for dma_transfer_number_config
    oled.o(i.OLED_Write_data_batch) refers to gd32f4xx_i2c.o(i.i2c_dma_config) for i2c_dma_config
    oled.o(i.OLED_Write_data_batch) refers to gd32f4xx_dma.o(i.dma_channel_enable) for dma_channel_enable
    oled.o(i.OLED_Write_data_batch) refers to gd32f4xx_dma.o(i.dma_flag_get) for dma_flag_get
    oled.o(i.OLED_Write_data_batch) refers to gd32f4xx_dma.o(i.dma_flag_clear) for dma_flag_clear
    oled.o(i.OLED_Write_data_batch) refers to gd32f4xx_dma.o(i.dma_channel_disable) for dma_channel_disable
    oled.o(i.OLED_Write_data_batch) refers to gd32f4xx_i2c.o(i.i2c_stop_on_bus) for i2c_stop_on_bus
    oled.o(i.OLED_Write_data_batch) refers to gd32f470vet6_bsp.o(.data) for oled_batch_buffer
    oled.o(i.OLED_Write_data_buffered) refers to oled.o(i.OLED_Flush_buffer) for OLED_Flush_buffer
    oled.o(i.OLED_Write_data_buffered) refers to gd32f470vet6_bsp.o(.data) for oled_buffer_index
    oled.o(i.OLED_Write_data_buffered) refers to oled.o(.data) for oled_last_write_time
    gd25qxx.o(i.spi_flash_buffer_read) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    gd25qxx.o(i.spi_flash_buffer_read) refers to gd25qxx.o(i.spi_flash_send_byte_dma) for spi_flash_send_byte_dma
    gd25qxx.o(i.spi_flash_buffer_read) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    gd25qxx.o(i.spi_flash_buffer_write) refers to gd25qxx.o(i.spi_flash_page_write) for spi_flash_page_write
    gd25qxx.o(i.spi_flash_bulk_erase) refers to gd25qxx.o(i.spi_flash_write_enable) for spi_flash_write_enable
    gd25qxx.o(i.spi_flash_bulk_erase) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    gd25qxx.o(i.spi_flash_bulk_erase) refers to gd25qxx.o(i.spi_flash_send_byte_dma) for spi_flash_send_byte_dma
    gd25qxx.o(i.spi_flash_bulk_erase) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    gd25qxx.o(i.spi_flash_bulk_erase) refers to gd25qxx.o(i.spi_flash_wait_for_write_end) for spi_flash_wait_for_write_end
    gd25qxx.o(i.spi_flash_init) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    gd25qxx.o(i.spi_flash_init) refers to gd32f4xx_spi.o(i.spi_enable) for spi_enable
    gd25qxx.o(i.spi_flash_page_write) refers to gd25qxx.o(i.spi_flash_write_enable) for spi_flash_write_enable
    gd25qxx.o(i.spi_flash_page_write) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    gd25qxx.o(i.spi_flash_page_write) refers to gd25qxx.o(i.spi_flash_send_byte_dma) for spi_flash_send_byte_dma
    gd25qxx.o(i.spi_flash_page_write) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    gd25qxx.o(i.spi_flash_page_write) refers to gd25qxx.o(i.spi_flash_wait_for_write_end) for spi_flash_wait_for_write_end
    gd25qxx.o(i.spi_flash_read_id) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    gd25qxx.o(i.spi_flash_read_id) refers to gd25qxx.o(i.spi_flash_send_byte_dma) for spi_flash_send_byte_dma
    gd25qxx.o(i.spi_flash_read_id) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    gd25qxx.o(i.spi_flash_sector_erase) refers to gd25qxx.o(i.spi_flash_write_enable) for spi_flash_write_enable
    gd25qxx.o(i.spi_flash_sector_erase) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    gd25qxx.o(i.spi_flash_sector_erase) refers to gd25qxx.o(i.spi_flash_send_byte_dma) for spi_flash_send_byte_dma
    gd25qxx.o(i.spi_flash_sector_erase) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    gd25qxx.o(i.spi_flash_sector_erase) refers to gd25qxx.o(i.spi_flash_wait_for_write_end) for spi_flash_wait_for_write_end
    gd25qxx.o(i.spi_flash_send_byte_dma) refers to gd32f4xx_dma.o(i.dma_deinit) for dma_deinit
    gd25qxx.o(i.spi_flash_send_byte_dma) refers to gd32f4xx_dma.o(i.dma_single_data_mode_init) for dma_single_data_mode_init
    gd25qxx.o(i.spi_flash_send_byte_dma) refers to gd32f4xx_dma.o(i.dma_channel_subperipheral_select) for dma_channel_subperipheral_select
    gd25qxx.o(i.spi_flash_send_byte_dma) refers to gd32f4xx_dma.o(i.dma_channel_enable) for dma_channel_enable
    gd25qxx.o(i.spi_flash_send_byte_dma) refers to gd32f4xx_spi.o(i.spi_dma_enable) for spi_dma_enable
    gd25qxx.o(i.spi_flash_send_byte_dma) refers to gd32f4xx_dma.o(i.dma_flag_get) for dma_flag_get
    gd25qxx.o(i.spi_flash_send_byte_dma) refers to gd32f4xx_spi.o(i.spi_dma_disable) for spi_dma_disable
    gd25qxx.o(i.spi_flash_send_byte_dma) refers to gd32f4xx_dma.o(i.dma_channel_disable) for dma_channel_disable
    gd25qxx.o(i.spi_flash_send_byte_dma) refers to gd32f4xx_dma.o(i.dma_flag_clear) for dma_flag_clear
    gd25qxx.o(i.spi_flash_send_byte_dma) refers to gd32f470vet6_bsp.o(.bss) for spi1_tx_buffer
    gd25qxx.o(i.spi_flash_send_halfword_dma) refers to gd32f4xx_dma.o(i.dma_deinit) for dma_deinit
    gd25qxx.o(i.spi_flash_send_halfword_dma) refers to gd32f4xx_dma.o(i.dma_single_data_mode_init) for dma_single_data_mode_init
    gd25qxx.o(i.spi_flash_send_halfword_dma) refers to gd32f4xx_dma.o(i.dma_channel_subperipheral_select) for dma_channel_subperipheral_select
    gd25qxx.o(i.spi_flash_send_halfword_dma) refers to gd32f4xx_dma.o(i.dma_channel_enable) for dma_channel_enable
    gd25qxx.o(i.spi_flash_send_halfword_dma) refers to gd32f4xx_spi.o(i.spi_dma_enable) for spi_dma_enable
    gd25qxx.o(i.spi_flash_send_halfword_dma) refers to gd32f4xx_dma.o(i.dma_flag_get) for dma_flag_get
    gd25qxx.o(i.spi_flash_send_halfword_dma) refers to gd32f4xx_spi.o(i.spi_dma_disable) for spi_dma_disable
    gd25qxx.o(i.spi_flash_send_halfword_dma) refers to gd32f4xx_dma.o(i.dma_channel_disable) for dma_channel_disable
    gd25qxx.o(i.spi_flash_send_halfword_dma) refers to gd32f4xx_dma.o(i.dma_flag_clear) for dma_flag_clear
    gd25qxx.o(i.spi_flash_send_halfword_dma) refers to gd32f470vet6_bsp.o(.bss) for spi1_tx_buffer
    gd25qxx.o(i.spi_flash_start_read_sequence) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    gd25qxx.o(i.spi_flash_start_read_sequence) refers to gd25qxx.o(i.spi_flash_send_byte_dma) for spi_flash_send_byte_dma
    gd25qxx.o(i.spi_flash_transmit_receive_dma) refers to gd32f4xx_dma.o(i.dma_deinit) for dma_deinit
    gd25qxx.o(i.spi_flash_transmit_receive_dma) refers to gd32f4xx_dma.o(i.dma_single_data_mode_init) for dma_single_data_mode_init
    gd25qxx.o(i.spi_flash_transmit_receive_dma) refers to gd32f4xx_dma.o(i.dma_channel_subperipheral_select) for dma_channel_subperipheral_select
    gd25qxx.o(i.spi_flash_transmit_receive_dma) refers to gd32f4xx_dma.o(i.dma_channel_enable) for dma_channel_enable
    gd25qxx.o(i.spi_flash_transmit_receive_dma) refers to gd32f4xx_spi.o(i.spi_dma_enable) for spi_dma_enable
    gd25qxx.o(i.spi_flash_transmit_receive_dma) refers to gd32f4xx_dma.o(i.dma_flag_get) for dma_flag_get
    gd25qxx.o(i.spi_flash_transmit_receive_dma) refers to gd32f4xx_spi.o(i.spi_dma_disable) for spi_dma_disable
    gd25qxx.o(i.spi_flash_transmit_receive_dma) refers to gd32f4xx_dma.o(i.dma_channel_disable) for dma_channel_disable
    gd25qxx.o(i.spi_flash_transmit_receive_dma) refers to gd32f4xx_dma.o(i.dma_flag_clear) for dma_flag_clear
    gd25qxx.o(i.spi_flash_transmit_receive_dma) refers to gd32f470vet6_bsp.o(.bss) for spi1_tx_buffer
    gd25qxx.o(i.spi_flash_wait_for_dma_end) refers to gd32f4xx_dma.o(i.dma_flag_get) for dma_flag_get
    gd25qxx.o(i.spi_flash_wait_for_dma_end) refers to gd32f4xx_dma.o(i.dma_flag_clear) for dma_flag_clear
    gd25qxx.o(i.spi_flash_wait_for_write_end) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    gd25qxx.o(i.spi_flash_wait_for_write_end) refers to gd25qxx.o(i.spi_flash_send_byte_dma) for spi_flash_send_byte_dma
    gd25qxx.o(i.spi_flash_wait_for_write_end) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    gd25qxx.o(i.spi_flash_write_enable) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    gd25qxx.o(i.spi_flash_write_enable) refers to gd25qxx.o(i.spi_flash_send_byte_dma) for spi_flash_send_byte_dma
    gd25qxx.o(i.spi_flash_write_enable) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    sdio_sdcard.o(i.cmdsent_error_check) refers to gd32f4xx_sdio.o(i.sdio_flag_get) for sdio_flag_get
    sdio_sdcard.o(i.cmdsent_error_check) refers to gd32f4xx_sdio.o(i.sdio_flag_clear) for sdio_flag_clear
    sdio_sdcard.o(i.dma_receive_config) refers to gd32f4xx_dma.o(i.dma_flag_clear) for dma_flag_clear
    sdio_sdcard.o(i.dma_receive_config) refers to gd32f4xx_dma.o(i.dma_channel_disable) for dma_channel_disable
    sdio_sdcard.o(i.dma_receive_config) refers to gd32f4xx_dma.o(i.dma_deinit) for dma_deinit
    sdio_sdcard.o(i.dma_receive_config) refers to gd32f4xx_dma.o(i.dma_multi_data_mode_init) for dma_multi_data_mode_init
    sdio_sdcard.o(i.dma_receive_config) refers to gd32f4xx_dma.o(i.dma_flow_controller_config) for dma_flow_controller_config
    sdio_sdcard.o(i.dma_receive_config) refers to gd32f4xx_dma.o(i.dma_channel_subperipheral_select) for dma_channel_subperipheral_select
    sdio_sdcard.o(i.dma_receive_config) refers to gd32f4xx_dma.o(i.dma_channel_enable) for dma_channel_enable
    sdio_sdcard.o(i.dma_transfer_config) refers to gd32f4xx_dma.o(i.dma_flag_clear) for dma_flag_clear
    sdio_sdcard.o(i.dma_transfer_config) refers to gd32f4xx_dma.o(i.dma_channel_disable) for dma_channel_disable
    sdio_sdcard.o(i.dma_transfer_config) refers to gd32f4xx_dma.o(i.dma_deinit) for dma_deinit
    sdio_sdcard.o(i.dma_transfer_config) refers to gd32f4xx_dma.o(i.dma_multi_data_mode_init) for dma_multi_data_mode_init
    sdio_sdcard.o(i.dma_transfer_config) refers to gd32f4xx_dma.o(i.dma_flow_controller_config) for dma_flow_controller_config
    sdio_sdcard.o(i.dma_transfer_config) refers to gd32f4xx_dma.o(i.dma_channel_subperipheral_select) for dma_channel_subperipheral_select
    sdio_sdcard.o(i.dma_transfer_config) refers to gd32f4xx_dma.o(i.dma_channel_enable) for dma_channel_enable
    sdio_sdcard.o(i.gpio_config) refers to gd32f4xx_gpio.o(i.gpio_af_set) for gpio_af_set
    sdio_sdcard.o(i.gpio_config) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    sdio_sdcard.o(i.gpio_config) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    sdio_sdcard.o(i.r1_error_check) refers to gd32f4xx_sdio.o(i.sdio_flag_clear) for sdio_flag_clear
    sdio_sdcard.o(i.r1_error_check) refers to gd32f4xx_sdio.o(i.sdio_command_index_get) for sdio_command_index_get
    sdio_sdcard.o(i.r1_error_check) refers to gd32f4xx_sdio.o(i.sdio_response_get) for sdio_response_get
    sdio_sdcard.o(i.r1_error_check) refers to sdio_sdcard.o(i.r1_error_type_check) for r1_error_type_check
    sdio_sdcard.o(i.r2_error_check) refers to gd32f4xx_sdio.o(i.sdio_flag_clear) for sdio_flag_clear
    sdio_sdcard.o(i.r3_error_check) refers to gd32f4xx_sdio.o(i.sdio_flag_clear) for sdio_flag_clear
    sdio_sdcard.o(i.r6_error_check) refers to gd32f4xx_sdio.o(i.sdio_flag_clear) for sdio_flag_clear
    sdio_sdcard.o(i.r6_error_check) refers to gd32f4xx_sdio.o(i.sdio_command_index_get) for sdio_command_index_get
    sdio_sdcard.o(i.r6_error_check) refers to gd32f4xx_sdio.o(i.sdio_response_get) for sdio_response_get
    sdio_sdcard.o(i.r7_error_check) refers to gd32f4xx_sdio.o(i.sdio_flag_clear) for sdio_flag_clear
    sdio_sdcard.o(i.rcu_config) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    sdio_sdcard.o(i.sd_block_read) refers to gd32f4xx_sdio.o(i.sdio_data_config) for sdio_data_config
    sdio_sdcard.o(i.sd_block_read) refers to gd32f4xx_sdio.o(i.sdio_data_transfer_config) for sdio_data_transfer_config
    sdio_sdcard.o(i.sd_block_read) refers to gd32f4xx_sdio.o(i.sdio_dsm_disable) for sdio_dsm_disable
    sdio_sdcard.o(i.sd_block_read) refers to gd32f4xx_sdio.o(i.sdio_dma_disable) for sdio_dma_disable
    sdio_sdcard.o(i.sd_block_read) refers to gd32f4xx_sdio.o(i.sdio_response_get) for sdio_response_get
    sdio_sdcard.o(i.sd_block_read) refers to sdio_sdcard.o(i.sd_datablocksize_get) for sd_datablocksize_get
    sdio_sdcard.o(i.sd_block_read) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdio_sdcard.o(i.sd_block_read) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdio_sdcard.o(i.sd_block_read) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdio_sdcard.o(i.sd_block_read) refers to sdio_sdcard.o(i.r1_error_check) for r1_error_check
    sdio_sdcard.o(i.sd_block_read) refers to gd32f4xx_sdio.o(i.sdio_dsm_enable) for sdio_dsm_enable
    sdio_sdcard.o(i.sd_block_read) refers to gd32f4xx_sdio.o(i.sdio_flag_get) for sdio_flag_get
    sdio_sdcard.o(i.sd_block_read) refers to gd32f4xx_sdio.o(i.sdio_data_read) for sdio_data_read
    sdio_sdcard.o(i.sd_block_read) refers to gd32f4xx_sdio.o(i.sdio_flag_clear) for sdio_flag_clear
    sdio_sdcard.o(i.sd_block_read) refers to gd32f4xx_sdio.o(i.sdio_interrupt_enable) for sdio_interrupt_enable
    sdio_sdcard.o(i.sd_block_read) refers to gd32f4xx_sdio.o(i.sdio_dma_enable) for sdio_dma_enable
    sdio_sdcard.o(i.sd_block_read) refers to sdio_sdcard.o(i.dma_receive_config) for dma_receive_config
    sdio_sdcard.o(i.sd_block_read) refers to gd32f4xx_dma.o(i.dma_flag_get) for dma_flag_get
    sdio_sdcard.o(i.sd_block_read) refers to sdio_sdcard.o(.data) for transerror
    sdio_sdcard.o(i.sd_block_write) refers to gd32f4xx_sdio.o(i.sdio_data_config) for sdio_data_config
    sdio_sdcard.o(i.sd_block_write) refers to gd32f4xx_sdio.o(i.sdio_data_transfer_config) for sdio_data_transfer_config
    sdio_sdcard.o(i.sd_block_write) refers to gd32f4xx_sdio.o(i.sdio_dsm_disable) for sdio_dsm_disable
    sdio_sdcard.o(i.sd_block_write) refers to gd32f4xx_sdio.o(i.sdio_dma_disable) for sdio_dma_disable
    sdio_sdcard.o(i.sd_block_write) refers to gd32f4xx_sdio.o(i.sdio_response_get) for sdio_response_get
    sdio_sdcard.o(i.sd_block_write) refers to sdio_sdcard.o(i.sd_datablocksize_get) for sd_datablocksize_get
    sdio_sdcard.o(i.sd_block_write) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdio_sdcard.o(i.sd_block_write) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdio_sdcard.o(i.sd_block_write) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdio_sdcard.o(i.sd_block_write) refers to sdio_sdcard.o(i.r1_error_check) for r1_error_check
    sdio_sdcard.o(i.sd_block_write) refers to gd32f4xx_sdio.o(i.sdio_dsm_enable) for sdio_dsm_enable
    sdio_sdcard.o(i.sd_block_write) refers to gd32f4xx_sdio.o(i.sdio_flag_get) for sdio_flag_get
    sdio_sdcard.o(i.sd_block_write) refers to gd32f4xx_sdio.o(i.sdio_data_write) for sdio_data_write
    sdio_sdcard.o(i.sd_block_write) refers to gd32f4xx_sdio.o(i.sdio_flag_clear) for sdio_flag_clear
    sdio_sdcard.o(i.sd_block_write) refers to gd32f4xx_sdio.o(i.sdio_interrupt_enable) for sdio_interrupt_enable
    sdio_sdcard.o(i.sd_block_write) refers to sdio_sdcard.o(i.dma_transfer_config) for dma_transfer_config
    sdio_sdcard.o(i.sd_block_write) refers to gd32f4xx_sdio.o(i.sdio_dma_enable) for sdio_dma_enable
    sdio_sdcard.o(i.sd_block_write) refers to gd32f4xx_dma.o(i.dma_flag_get) for dma_flag_get
    sdio_sdcard.o(i.sd_block_write) refers to sdio_sdcard.o(i.sd_card_state_get) for sd_card_state_get
    sdio_sdcard.o(i.sd_block_write) refers to sdio_sdcard.o(.data) for transerror
    sdio_sdcard.o(i.sd_bus_mode_config) refers to sdio_sdcard.o(i.sd_bus_width_config) for sd_bus_width_config
    sdio_sdcard.o(i.sd_bus_mode_config) refers to gd32f4xx_sdio.o(i.sdio_clock_config) for sdio_clock_config
    sdio_sdcard.o(i.sd_bus_mode_config) refers to gd32f4xx_sdio.o(i.sdio_bus_mode_set) for sdio_bus_mode_set
    sdio_sdcard.o(i.sd_bus_mode_config) refers to gd32f4xx_sdio.o(i.sdio_hardware_clock_disable) for sdio_hardware_clock_disable
    sdio_sdcard.o(i.sd_bus_mode_config) refers to sdio_sdcard.o(.data) for cardtype
    sdio_sdcard.o(i.sd_bus_width_config) refers to gd32f4xx_sdio.o(i.sdio_response_get) for sdio_response_get
    sdio_sdcard.o(i.sd_bus_width_config) refers to sdio_sdcard.o(i.sd_scr_get) for sd_scr_get
    sdio_sdcard.o(i.sd_bus_width_config) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdio_sdcard.o(i.sd_bus_width_config) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdio_sdcard.o(i.sd_bus_width_config) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdio_sdcard.o(i.sd_bus_width_config) refers to sdio_sdcard.o(i.r1_error_check) for r1_error_check
    sdio_sdcard.o(i.sd_bus_width_config) refers to sdio_sdcard.o(.data) for sd_scr
    sdio_sdcard.o(i.sd_card_capacity_get) refers to sdio_sdcard.o(.data) for cardtype
    sdio_sdcard.o(i.sd_card_capacity_get) refers to sdio_sdcard.o(.bss) for sd_csd
    sdio_sdcard.o(i.sd_card_information_get) refers to sdio_sdcard.o(.data) for cardtype
    sdio_sdcard.o(i.sd_card_information_get) refers to sdio_sdcard.o(.bss) for sd_cid
    sdio_sdcard.o(i.sd_card_init) refers to gd32f4xx_sdio.o(i.sdio_power_state_get) for sdio_power_state_get
    sdio_sdcard.o(i.sd_card_init) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdio_sdcard.o(i.sd_card_init) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdio_sdcard.o(i.sd_card_init) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdio_sdcard.o(i.sd_card_init) refers to sdio_sdcard.o(i.r2_error_check) for r2_error_check
    sdio_sdcard.o(i.sd_card_init) refers to gd32f4xx_sdio.o(i.sdio_response_get) for sdio_response_get
    sdio_sdcard.o(i.sd_card_init) refers to sdio_sdcard.o(i.r6_error_check) for r6_error_check
    sdio_sdcard.o(i.sd_card_init) refers to sdio_sdcard.o(.data) for cardtype
    sdio_sdcard.o(i.sd_card_init) refers to sdio_sdcard.o(.bss) for sd_cid
    sdio_sdcard.o(i.sd_card_select_deselect) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdio_sdcard.o(i.sd_card_select_deselect) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdio_sdcard.o(i.sd_card_select_deselect) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdio_sdcard.o(i.sd_card_select_deselect) refers to sdio_sdcard.o(i.r1_error_check) for r1_error_check
    sdio_sdcard.o(i.sd_card_state_get) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdio_sdcard.o(i.sd_card_state_get) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdio_sdcard.o(i.sd_card_state_get) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdio_sdcard.o(i.sd_card_state_get) refers to gd32f4xx_sdio.o(i.sdio_flag_clear) for sdio_flag_clear
    sdio_sdcard.o(i.sd_card_state_get) refers to gd32f4xx_sdio.o(i.sdio_command_index_get) for sdio_command_index_get
    sdio_sdcard.o(i.sd_card_state_get) refers to gd32f4xx_sdio.o(i.sdio_response_get) for sdio_response_get
    sdio_sdcard.o(i.sd_card_state_get) refers to sdio_sdcard.o(i.r1_error_type_check) for r1_error_type_check
    sdio_sdcard.o(i.sd_card_state_get) refers to sdio_sdcard.o(.data) for sd_rca
    sdio_sdcard.o(i.sd_cardstatus_get) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdio_sdcard.o(i.sd_cardstatus_get) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdio_sdcard.o(i.sd_cardstatus_get) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdio_sdcard.o(i.sd_cardstatus_get) refers to sdio_sdcard.o(i.r1_error_check) for r1_error_check
    sdio_sdcard.o(i.sd_cardstatus_get) refers to gd32f4xx_sdio.o(i.sdio_response_get) for sdio_response_get
    sdio_sdcard.o(i.sd_cardstatus_get) refers to sdio_sdcard.o(.data) for sd_rca
    sdio_sdcard.o(i.sd_erase) refers to gd32f4xx_sdio.o(i.sdio_response_get) for sdio_response_get
    sdio_sdcard.o(i.sd_erase) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdio_sdcard.o(i.sd_erase) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdio_sdcard.o(i.sd_erase) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdio_sdcard.o(i.sd_erase) refers to sdio_sdcard.o(i.r1_error_check) for r1_error_check
    sdio_sdcard.o(i.sd_erase) refers to sdio_sdcard.o(i.sd_card_state_get) for sd_card_state_get
    sdio_sdcard.o(i.sd_erase) refers to sdio_sdcard.o(.bss) for sd_csd
    sdio_sdcard.o(i.sd_erase) refers to sdio_sdcard.o(.data) for cardtype
    sdio_sdcard.o(i.sd_init) refers to sdio_sdcard.o(i.rcu_config) for rcu_config
    sdio_sdcard.o(i.sd_init) refers to sdio_sdcard.o(i.gpio_config) for gpio_config
    sdio_sdcard.o(i.sd_init) refers to gd32f4xx_sdio.o(i.sdio_deinit) for sdio_deinit
    sdio_sdcard.o(i.sd_init) refers to sdio_sdcard.o(i.sd_power_on) for sd_power_on
    sdio_sdcard.o(i.sd_init) refers to sdio_sdcard.o(i.sd_card_init) for sd_card_init
    sdio_sdcard.o(i.sd_init) refers to gd32f4xx_sdio.o(i.sdio_clock_config) for sdio_clock_config
    sdio_sdcard.o(i.sd_init) refers to gd32f4xx_sdio.o(i.sdio_bus_mode_set) for sdio_bus_mode_set
    sdio_sdcard.o(i.sd_init) refers to gd32f4xx_sdio.o(i.sdio_hardware_clock_disable) for sdio_hardware_clock_disable
    sdio_sdcard.o(i.sd_interrupts_process) refers to gd32f4xx_sdio.o(i.sdio_interrupt_flag_get) for sdio_interrupt_flag_get
    sdio_sdcard.o(i.sd_interrupts_process) refers to sdio_sdcard.o(i.sd_transfer_stop) for sd_transfer_stop
    sdio_sdcard.o(i.sd_interrupts_process) refers to gd32f4xx_sdio.o(i.sdio_interrupt_flag_clear) for sdio_interrupt_flag_clear
    sdio_sdcard.o(i.sd_interrupts_process) refers to gd32f4xx_sdio.o(i.sdio_interrupt_disable) for sdio_interrupt_disable
    sdio_sdcard.o(i.sd_interrupts_process) refers to sdio_sdcard.o(.data) for transerror
    sdio_sdcard.o(i.sd_lock_unlock) refers to gd32f4xx_sdio.o(i.sdio_data_config) for sdio_data_config
    sdio_sdcard.o(i.sd_lock_unlock) refers to gd32f4xx_sdio.o(i.sdio_data_transfer_config) for sdio_data_transfer_config
    sdio_sdcard.o(i.sd_lock_unlock) refers to gd32f4xx_sdio.o(i.sdio_dsm_disable) for sdio_dsm_disable
    sdio_sdcard.o(i.sd_lock_unlock) refers to gd32f4xx_sdio.o(i.sdio_dma_disable) for sdio_dma_disable
    sdio_sdcard.o(i.sd_lock_unlock) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdio_sdcard.o(i.sd_lock_unlock) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdio_sdcard.o(i.sd_lock_unlock) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdio_sdcard.o(i.sd_lock_unlock) refers to sdio_sdcard.o(i.r1_error_check) for r1_error_check
    sdio_sdcard.o(i.sd_lock_unlock) refers to gd32f4xx_sdio.o(i.sdio_response_get) for sdio_response_get
    sdio_sdcard.o(i.sd_lock_unlock) refers to gd32f4xx_sdio.o(i.sdio_dsm_enable) for sdio_dsm_enable
    sdio_sdcard.o(i.sd_lock_unlock) refers to gd32f4xx_sdio.o(i.sdio_data_write) for sdio_data_write
    sdio_sdcard.o(i.sd_lock_unlock) refers to gd32f4xx_sdio.o(i.sdio_flag_get) for sdio_flag_get
    sdio_sdcard.o(i.sd_lock_unlock) refers to gd32f4xx_sdio.o(i.sdio_flag_clear) for sdio_flag_clear
    sdio_sdcard.o(i.sd_lock_unlock) refers to sdio_sdcard.o(i.sd_card_state_get) for sd_card_state_get
    sdio_sdcard.o(i.sd_lock_unlock) refers to sdio_sdcard.o(.bss) for sd_csd
    sdio_sdcard.o(i.sd_lock_unlock) refers to sdio_sdcard.o(.data) for sd_rca
    sdio_sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_sdio.o(i.sdio_data_config) for sdio_data_config
    sdio_sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_sdio.o(i.sdio_data_transfer_config) for sdio_data_transfer_config
    sdio_sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_sdio.o(i.sdio_dsm_disable) for sdio_dsm_disable
    sdio_sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_sdio.o(i.sdio_dma_disable) for sdio_dma_disable
    sdio_sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_sdio.o(i.sdio_response_get) for sdio_response_get
    sdio_sdcard.o(i.sd_multiblocks_read) refers to sdio_sdcard.o(i.sd_datablocksize_get) for sd_datablocksize_get
    sdio_sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdio_sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdio_sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdio_sdcard.o(i.sd_multiblocks_read) refers to sdio_sdcard.o(i.r1_error_check) for r1_error_check
    sdio_sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_sdio.o(i.sdio_dsm_enable) for sdio_dsm_enable
    sdio_sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_sdio.o(i.sdio_flag_get) for sdio_flag_get
    sdio_sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_sdio.o(i.sdio_data_read) for sdio_data_read
    sdio_sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_sdio.o(i.sdio_flag_clear) for sdio_flag_clear
    sdio_sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_sdio.o(i.sdio_interrupt_enable) for sdio_interrupt_enable
    sdio_sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_sdio.o(i.sdio_dma_enable) for sdio_dma_enable
    sdio_sdcard.o(i.sd_multiblocks_read) refers to sdio_sdcard.o(i.dma_receive_config) for dma_receive_config
    sdio_sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_dma.o(i.dma_flag_get) for dma_flag_get
    sdio_sdcard.o(i.sd_multiblocks_read) refers to sdio_sdcard.o(.data) for transerror
    sdio_sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_sdio.o(i.sdio_data_config) for sdio_data_config
    sdio_sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_sdio.o(i.sdio_data_transfer_config) for sdio_data_transfer_config
    sdio_sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_sdio.o(i.sdio_dsm_disable) for sdio_dsm_disable
    sdio_sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_sdio.o(i.sdio_dma_disable) for sdio_dma_disable
    sdio_sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_sdio.o(i.sdio_response_get) for sdio_response_get
    sdio_sdcard.o(i.sd_multiblocks_write) refers to sdio_sdcard.o(i.sd_datablocksize_get) for sd_datablocksize_get
    sdio_sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdio_sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdio_sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdio_sdcard.o(i.sd_multiblocks_write) refers to sdio_sdcard.o(i.r1_error_check) for r1_error_check
    sdio_sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_sdio.o(i.sdio_dsm_enable) for sdio_dsm_enable
    sdio_sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_sdio.o(i.sdio_flag_get) for sdio_flag_get
    sdio_sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_sdio.o(i.sdio_data_write) for sdio_data_write
    sdio_sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_sdio.o(i.sdio_flag_clear) for sdio_flag_clear
    sdio_sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_sdio.o(i.sdio_interrupt_enable) for sdio_interrupt_enable
    sdio_sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_sdio.o(i.sdio_dma_enable) for sdio_dma_enable
    sdio_sdcard.o(i.sd_multiblocks_write) refers to sdio_sdcard.o(i.dma_transfer_config) for dma_transfer_config
    sdio_sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_dma.o(i.dma_flag_get) for dma_flag_get
    sdio_sdcard.o(i.sd_multiblocks_write) refers to sdio_sdcard.o(i.sd_card_state_get) for sd_card_state_get
    sdio_sdcard.o(i.sd_multiblocks_write) refers to sdio_sdcard.o(.data) for transerror
    sdio_sdcard.o(i.sd_power_off) refers to gd32f4xx_sdio.o(i.sdio_power_state_set) for sdio_power_state_set
    sdio_sdcard.o(i.sd_power_on) refers to gd32f4xx_sdio.o(i.sdio_clock_config) for sdio_clock_config
    sdio_sdcard.o(i.sd_power_on) refers to gd32f4xx_sdio.o(i.sdio_bus_mode_set) for sdio_bus_mode_set
    sdio_sdcard.o(i.sd_power_on) refers to gd32f4xx_sdio.o(i.sdio_hardware_clock_disable) for sdio_hardware_clock_disable
    sdio_sdcard.o(i.sd_power_on) refers to gd32f4xx_sdio.o(i.sdio_power_state_set) for sdio_power_state_set
    sdio_sdcard.o(i.sd_power_on) refers to gd32f4xx_sdio.o(i.sdio_clock_enable) for sdio_clock_enable
    sdio_sdcard.o(i.sd_power_on) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdio_sdcard.o(i.sd_power_on) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdio_sdcard.o(i.sd_power_on) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdio_sdcard.o(i.sd_power_on) refers to sdio_sdcard.o(i.cmdsent_error_check) for cmdsent_error_check
    sdio_sdcard.o(i.sd_power_on) refers to sdio_sdcard.o(i.r7_error_check) for r7_error_check
    sdio_sdcard.o(i.sd_power_on) refers to sdio_sdcard.o(i.r1_error_check) for r1_error_check
    sdio_sdcard.o(i.sd_power_on) refers to sdio_sdcard.o(i.r3_error_check) for r3_error_check
    sdio_sdcard.o(i.sd_power_on) refers to gd32f4xx_sdio.o(i.sdio_response_get) for sdio_response_get
    sdio_sdcard.o(i.sd_power_on) refers to sdio_sdcard.o(.data) for cardtype
    sdio_sdcard.o(i.sd_scr_get) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdio_sdcard.o(i.sd_scr_get) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdio_sdcard.o(i.sd_scr_get) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdio_sdcard.o(i.sd_scr_get) refers to sdio_sdcard.o(i.r1_error_check) for r1_error_check
    sdio_sdcard.o(i.sd_scr_get) refers to gd32f4xx_sdio.o(i.sdio_data_config) for sdio_data_config
    sdio_sdcard.o(i.sd_scr_get) refers to gd32f4xx_sdio.o(i.sdio_data_transfer_config) for sdio_data_transfer_config
    sdio_sdcard.o(i.sd_scr_get) refers to gd32f4xx_sdio.o(i.sdio_dsm_enable) for sdio_dsm_enable
    sdio_sdcard.o(i.sd_scr_get) refers to gd32f4xx_sdio.o(i.sdio_flag_get) for sdio_flag_get
    sdio_sdcard.o(i.sd_scr_get) refers to gd32f4xx_sdio.o(i.sdio_data_read) for sdio_data_read
    sdio_sdcard.o(i.sd_scr_get) refers to gd32f4xx_sdio.o(i.sdio_flag_clear) for sdio_flag_clear
    sdio_sdcard.o(i.sd_sdstatus_get) refers to gd32f4xx_sdio.o(i.sdio_response_get) for sdio_response_get
    sdio_sdcard.o(i.sd_sdstatus_get) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdio_sdcard.o(i.sd_sdstatus_get) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdio_sdcard.o(i.sd_sdstatus_get) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdio_sdcard.o(i.sd_sdstatus_get) refers to sdio_sdcard.o(i.r1_error_check) for r1_error_check
    sdio_sdcard.o(i.sd_sdstatus_get) refers to gd32f4xx_sdio.o(i.sdio_data_config) for sdio_data_config
    sdio_sdcard.o(i.sd_sdstatus_get) refers to gd32f4xx_sdio.o(i.sdio_data_transfer_config) for sdio_data_transfer_config
    sdio_sdcard.o(i.sd_sdstatus_get) refers to gd32f4xx_sdio.o(i.sdio_dsm_enable) for sdio_dsm_enable
    sdio_sdcard.o(i.sd_sdstatus_get) refers to gd32f4xx_sdio.o(i.sdio_flag_get) for sdio_flag_get
    sdio_sdcard.o(i.sd_sdstatus_get) refers to gd32f4xx_sdio.o(i.sdio_data_read) for sdio_data_read
    sdio_sdcard.o(i.sd_sdstatus_get) refers to gd32f4xx_sdio.o(i.sdio_flag_clear) for sdio_flag_clear
    sdio_sdcard.o(i.sd_sdstatus_get) refers to sdio_sdcard.o(.data) for sd_rca
    sdio_sdcard.o(i.sd_transfer_mode_config) refers to sdio_sdcard.o(.data) for transmode
    sdio_sdcard.o(i.sd_transfer_state_get) refers to gd32f4xx_sdio.o(i.sdio_flag_get) for sdio_flag_get
    sdio_sdcard.o(i.sd_transfer_stop) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdio_sdcard.o(i.sd_transfer_stop) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdio_sdcard.o(i.sd_transfer_stop) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdio_sdcard.o(i.sd_transfer_stop) refers to sdio_sdcard.o(i.r1_error_check) for r1_error_check
    diskio.o(i.disk_initialize) refers to sdio_sdcard.o(i.sd_init) for sd_init
    diskio.o(i.disk_initialize) refers to sdio_sdcard.o(i.sd_card_information_get) for sd_card_information_get
    diskio.o(i.disk_initialize) refers to sdio_sdcard.o(i.sd_card_select_deselect) for sd_card_select_deselect
    diskio.o(i.disk_initialize) refers to sdio_sdcard.o(i.sd_cardstatus_get) for sd_cardstatus_get
    diskio.o(i.disk_initialize) refers to sdio_sdcard.o(i.sd_bus_mode_config) for sd_bus_mode_config
    diskio.o(i.disk_initialize) refers to sdio_sdcard.o(i.sd_transfer_mode_config) for sd_transfer_mode_config
    diskio.o(i.disk_read) refers to sdio_sdcard.o(i.sd_block_read) for sd_block_read
    diskio.o(i.disk_read) refers to sdio_sdcard.o(i.sd_multiblocks_read) for sd_multiblocks_read
    diskio.o(i.disk_write) refers to sdio_sdcard.o(i.sd_block_write) for sd_block_write
    diskio.o(i.disk_write) refers to sdio_sdcard.o(i.sd_multiblocks_write) for sd_multiblocks_write
    ff.o(i.check_fs) refers to diskio.o(i.disk_read) for disk_read
    ff.o(i.chk_mounted) refers to diskio.o(i.disk_status) for disk_status
    ff.o(i.chk_mounted) refers to diskio.o(i.disk_initialize) for disk_initialize
    ff.o(i.chk_mounted) refers to ff.o(i.check_fs) for check_fs
    ff.o(i.chk_mounted) refers to diskio.o(i.disk_read) for disk_read
    ff.o(i.chk_mounted) refers to ff.o(.data) for FatFs
    ff.o(i.cmp_lfn) refers to unicode.o(i.ff_wtoupper) for ff_wtoupper
    ff.o(i.cmp_lfn) refers to ff.o(.constdata) for LfnOfs
    ff.o(i.create_chain) refers to ff.o(i.get_fat) for get_fat
    ff.o(i.create_chain) refers to ff.o(i.put_fat) for put_fat
    ff.o(i.create_name) refers to unicode.o(i.ff_convert) for ff_convert
    ff.o(i.create_name) refers to ff.o(i.chk_chr) for chk_chr
    ff.o(i.create_name) refers to ff.o(i.mem_set) for mem_set
    ff.o(i.create_name) refers to unicode.o(i.ff_wtoupper) for ff_wtoupper
    ff.o(i.dir_find) refers to ff.o(i.dir_sdi) for dir_sdi
    ff.o(i.dir_find) refers to ff.o(i.move_window) for move_window
    ff.o(i.dir_find) refers to ff.o(i.cmp_lfn) for cmp_lfn
    ff.o(i.dir_find) refers to ff.o(i.sum_sfn) for sum_sfn
    ff.o(i.dir_find) refers to ff.o(i.mem_cmp) for mem_cmp
    ff.o(i.dir_find) refers to ff.o(i.dir_next) for dir_next
    ff.o(i.dir_next) refers to ff.o(i.get_fat) for get_fat
    ff.o(i.dir_next) refers to ff.o(i.create_chain) for create_chain
    ff.o(i.dir_next) refers to ff.o(i.move_window) for move_window
    ff.o(i.dir_next) refers to ff.o(i.mem_set) for mem_set
    ff.o(i.dir_next) refers to ff.o(i.clust2sect) for clust2sect
    ff.o(i.dir_read) refers to ff.o(i.move_window) for move_window
    ff.o(i.dir_read) refers to ff.o(i.pick_lfn) for pick_lfn
    ff.o(i.dir_read) refers to ff.o(i.sum_sfn) for sum_sfn
    ff.o(i.dir_read) refers to ff.o(i.dir_next) for dir_next
    ff.o(i.dir_register) refers to ff.o(i.mem_cpy) for mem_cpy
    ff.o(i.dir_register) refers to ff.o(i.gen_numname) for gen_numname
    ff.o(i.dir_register) refers to ff.o(i.dir_find) for dir_find
    ff.o(i.dir_register) refers to ff.o(i.dir_sdi) for dir_sdi
    ff.o(i.dir_register) refers to ff.o(i.move_window) for move_window
    ff.o(i.dir_register) refers to ff.o(i.dir_next) for dir_next
    ff.o(i.dir_register) refers to ff.o(i.sum_sfn) for sum_sfn
    ff.o(i.dir_register) refers to ff.o(i.fit_lfn) for fit_lfn
    ff.o(i.dir_register) refers to ff.o(i.mem_set) for mem_set
    ff.o(i.dir_remove) refers to ff.o(i.dir_sdi) for dir_sdi
    ff.o(i.dir_remove) refers to ff.o(i.move_window) for move_window
    ff.o(i.dir_remove) refers to ff.o(i.dir_next) for dir_next
    ff.o(i.dir_sdi) refers to ff.o(i.get_fat) for get_fat
    ff.o(i.dir_sdi) refers to ff.o(i.clust2sect) for clust2sect
    ff.o(i.f_chmod) refers to ff.o(i.chk_mounted) for chk_mounted
    ff.o(i.f_chmod) refers to ff.o(i.follow_path) for follow_path
    ff.o(i.f_chmod) refers to ff.o(i.sync) for sync
    ff.o(i.f_close) refers to ff.o(i.f_sync) for f_sync
    ff.o(i.f_getfree) refers to ff.o(i.chk_mounted) for chk_mounted
    ff.o(i.f_getfree) refers to ff.o(i.get_fat) for get_fat
    ff.o(i.f_getfree) refers to ff.o(i.move_window) for move_window
    ff.o(i.f_gets) refers to ff.o(i.f_read) for f_read
    ff.o(i.f_lseek) refers to ff.o(i.validate) for validate
    ff.o(i.f_lseek) refers to ff.o(i.create_chain) for create_chain
    ff.o(i.f_lseek) refers to ff.o(i.get_fat) for get_fat
    ff.o(i.f_lseek) refers to ff.o(i.clust2sect) for clust2sect
    ff.o(i.f_lseek) refers to diskio.o(i.disk_write) for disk_write
    ff.o(i.f_lseek) refers to diskio.o(i.disk_read) for disk_read
    ff.o(i.f_mkdir) refers to diskio.o(i.get_fattime) for get_fattime
    ff.o(i.f_mkdir) refers to ff.o(i.chk_mounted) for chk_mounted
    ff.o(i.f_mkdir) refers to ff.o(i.follow_path) for follow_path
    ff.o(i.f_mkdir) refers to ff.o(i.create_chain) for create_chain
    ff.o(i.f_mkdir) refers to ff.o(i.move_window) for move_window
    ff.o(i.f_mkdir) refers to ff.o(i.clust2sect) for clust2sect
    ff.o(i.f_mkdir) refers to ff.o(i.mem_set) for mem_set
    ff.o(i.f_mkdir) refers to ff.o(i.mem_cpy) for mem_cpy
    ff.o(i.f_mkdir) refers to ff.o(i.dir_register) for dir_register
    ff.o(i.f_mkdir) refers to ff.o(i.remove_chain) for remove_chain
    ff.o(i.f_mkdir) refers to ff.o(i.sync) for sync
    ff.o(i.f_mount) refers to ff.o(.data) for FatFs
    ff.o(i.f_open) refers to ff.o(i.chk_mounted) for chk_mounted
    ff.o(i.f_open) refers to ff.o(i.follow_path) for follow_path
    ff.o(i.f_open) refers to ff.o(i.dir_register) for dir_register
    ff.o(i.f_open) refers to diskio.o(i.get_fattime) for get_fattime
    ff.o(i.f_open) refers to ff.o(i.remove_chain) for remove_chain
    ff.o(i.f_open) refers to ff.o(i.move_window) for move_window
    ff.o(i.f_opendir) refers to ff.o(i.chk_mounted) for chk_mounted
    ff.o(i.f_opendir) refers to ff.o(i.follow_path) for follow_path
    ff.o(i.f_opendir) refers to ff.o(i.dir_sdi) for dir_sdi
    ff.o(i.f_printf) refers to ff.o(i.f_putc) for f_putc
    ff.o(i.f_printf) refers to ff.o(i.f_puts) for f_puts
    ff.o(i.f_putc) refers to ff.o(i.f_write) for f_write
    ff.o(i.f_puts) refers to ff.o(i.f_putc) for f_putc
    ff.o(i.f_read) refers to ff.o(i.validate) for validate
    ff.o(i.f_read) refers to ff.o(i.get_fat) for get_fat
    ff.o(i.f_read) refers to ff.o(i.clust2sect) for clust2sect
    ff.o(i.f_read) refers to diskio.o(i.disk_read) for disk_read
    ff.o(i.f_read) refers to ff.o(i.mem_cpy) for mem_cpy
    ff.o(i.f_read) refers to diskio.o(i.disk_write) for disk_write
    ff.o(i.f_readdir) refers to ff.o(i.validate) for validate
    ff.o(i.f_readdir) refers to ff.o(i.dir_sdi) for dir_sdi
    ff.o(i.f_readdir) refers to ff.o(i.dir_read) for dir_read
    ff.o(i.f_readdir) refers to ff.o(i.get_fileinfo) for get_fileinfo
    ff.o(i.f_readdir) refers to ff.o(i.dir_next) for dir_next
    ff.o(i.f_rename) refers to ff.o(i.chk_mounted) for chk_mounted
    ff.o(i.f_rename) refers to ff.o(i.follow_path) for follow_path
    ff.o(i.f_rename) refers to ff.o(i.mem_cpy) for mem_cpy
    ff.o(i.f_rename) refers to ff.o(i.dir_register) for dir_register
    ff.o(i.f_rename) refers to ff.o(i.clust2sect) for clust2sect
    ff.o(i.f_rename) refers to ff.o(i.move_window) for move_window
    ff.o(i.f_rename) refers to ff.o(i.dir_remove) for dir_remove
    ff.o(i.f_rename) refers to ff.o(i.sync) for sync
    ff.o(i.f_stat) refers to ff.o(i.chk_mounted) for chk_mounted
    ff.o(i.f_stat) refers to ff.o(i.follow_path) for follow_path
    ff.o(i.f_stat) refers to ff.o(i.get_fileinfo) for get_fileinfo
    ff.o(i.f_sync) refers to ff.o(i.validate) for validate
    ff.o(i.f_sync) refers to diskio.o(i.disk_write) for disk_write
    ff.o(i.f_sync) refers to ff.o(i.move_window) for move_window
    ff.o(i.f_sync) refers to diskio.o(i.get_fattime) for get_fattime
    ff.o(i.f_sync) refers to ff.o(i.sync) for sync
    ff.o(i.f_truncate) refers to ff.o(i.validate) for validate
    ff.o(i.f_truncate) refers to ff.o(i.remove_chain) for remove_chain
    ff.o(i.f_truncate) refers to ff.o(i.get_fat) for get_fat
    ff.o(i.f_truncate) refers to ff.o(i.put_fat) for put_fat
    ff.o(i.f_unlink) refers to ff.o(i.chk_mounted) for chk_mounted
    ff.o(i.f_unlink) refers to ff.o(i.follow_path) for follow_path
    ff.o(i.f_unlink) refers to ff.o(i.mem_cpy) for mem_cpy
    ff.o(i.f_unlink) refers to ff.o(i.dir_sdi) for dir_sdi
    ff.o(i.f_unlink) refers to ff.o(i.dir_read) for dir_read
    ff.o(i.f_unlink) refers to ff.o(i.dir_remove) for dir_remove
    ff.o(i.f_unlink) refers to ff.o(i.remove_chain) for remove_chain
    ff.o(i.f_unlink) refers to ff.o(i.sync) for sync
    ff.o(i.f_utime) refers to ff.o(i.chk_mounted) for chk_mounted
    ff.o(i.f_utime) refers to ff.o(i.follow_path) for follow_path
    ff.o(i.f_utime) refers to ff.o(i.sync) for sync
    ff.o(i.f_write) refers to ff.o(i.validate) for validate
    ff.o(i.f_write) refers to ff.o(i.create_chain) for create_chain
    ff.o(i.f_write) refers to diskio.o(i.disk_write) for disk_write
    ff.o(i.f_write) refers to ff.o(i.clust2sect) for clust2sect
    ff.o(i.f_write) refers to ff.o(i.mem_cpy) for mem_cpy
    ff.o(i.f_write) refers to diskio.o(i.disk_read) for disk_read
    ff.o(i.fit_lfn) refers to ff.o(.constdata) for LfnOfs
    ff.o(i.follow_path) refers to ff.o(i.dir_sdi) for dir_sdi
    ff.o(i.follow_path) refers to ff.o(i.create_name) for create_name
    ff.o(i.follow_path) refers to ff.o(i.dir_find) for dir_find
    ff.o(i.gen_numname) refers to ff.o(i.mem_cpy) for mem_cpy
    ff.o(i.get_fat) refers to ff.o(i.move_window) for move_window
    ff.o(i.get_fileinfo) refers to unicode.o(i.ff_convert) for ff_convert
    ff.o(i.move_window) refers to diskio.o(i.disk_write) for disk_write
    ff.o(i.move_window) refers to diskio.o(i.disk_read) for disk_read
    ff.o(i.pick_lfn) refers to ff.o(.constdata) for LfnOfs
    ff.o(i.put_fat) refers to ff.o(i.move_window) for move_window
    ff.o(i.remove_chain) refers to ff.o(i.get_fat) for get_fat
    ff.o(i.remove_chain) refers to ff.o(i.put_fat) for put_fat
    ff.o(i.sync) refers to ff.o(i.move_window) for move_window
    ff.o(i.sync) refers to ff.o(i.mem_set) for mem_set
    ff.o(i.sync) refers to diskio.o(i.disk_write) for disk_write
    ff.o(i.sync) refers to diskio.o(i.disk_ioctl) for disk_ioctl
    ff.o(i.validate) refers to diskio.o(i.disk_status) for disk_status
    unicode.o(i.ff_wtoupper) refers to unicode.o(.constdata) for tbl_lower
    gd30ad3344.o(i.AD3344_Send_Data) refers to gd30ad3344.o(i.ad3344_spi_txrx16bit) for ad3344_spi_txrx16bit
    gd30ad3344.o(i.ad3344_init) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    gd30ad3344.o(i.ad3344_init) refers to gd30ad3344.o(i.delay_us) for delay_us
    gd30ad3344.o(i.ad3344_init) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    gd30ad3344.o(i.ad3344_init) refers to gd30ad3344.o(i.ad3344_spi_txrx16bit) for ad3344_spi_txrx16bit
    gd30ad3344.o(i.ad3344_init) refers to gd30ad3344.o(.data) for AD3344_CONFIG
    gd30ad3344.o(i.ad3344_read_data16) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    gd30ad3344.o(i.ad3344_read_data16) refers to gd30ad3344.o(i.delay_us) for delay_us
    gd30ad3344.o(i.ad3344_read_data16) refers to gd30ad3344.o(i.AD3344_Send_Data) for AD3344_Send_Data
    gd30ad3344.o(i.ad3344_read_data16) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    gd30ad3344.o(i.ad3344_read_data32) refers to gd30ad3344.o(i.AD3344_Send_Data) for AD3344_Send_Data
    gd30ad3344.o(i.ad3344_read_regs) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    gd30ad3344.o(i.ad3344_read_regs) refers to gd30ad3344.o(i.delay_us) for delay_us
    gd30ad3344.o(i.ad3344_read_regs) refers to gd30ad3344.o(i.AD3344_Send_Data) for AD3344_Send_Data
    gd30ad3344.o(i.ad3344_read_regs) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    gd30ad3344.o(i.ad3344_reset) refers to gd30ad3344.o(i.ad3344_spi_txrx16bit) for ad3344_spi_txrx16bit
    gd30ad3344.o(i.ad3344_spi_txrx16bit) refers to gd32f4xx_spi.o(i.spi_i2s_flag_get) for spi_i2s_flag_get
    gd30ad3344.o(i.ad3344_spi_txrx16bit) refers to gd32f4xx_spi.o(i.spi_i2s_data_transmit) for spi_i2s_data_transmit
    gd30ad3344.o(i.ad3344_spi_txrx16bit) refers to gd32f4xx_spi.o(i.spi_i2s_data_receive) for spi_i2s_data_receive
    gd30ad3344.o(i.ad3344_stop_conver) refers to gd30ad3344.o(i.ad3344_spi_txrx16bit) for ad3344_spi_txrx16bit
    gd30ad3344.o(i.ad3344_stop_conver) refers to gd30ad3344.o(.data) for AD3344_CONFIG
    system_gd32f4xx.o(i.SystemCoreClockUpdate) refers to system_gd32f4xx.o(.data) for SystemCoreClock
    system_gd32f4xx.o(i.SystemInit) refers to system_gd32f4xx.o(i._soft_delay_) for _soft_delay_
    system_gd32f4xx.o(i.SystemInit) refers to system_gd32f4xx.o(i.system_clock_config) for system_clock_config
    system_gd32f4xx.o(i.system_clock_config) refers to system_gd32f4xx.o(i.system_clock_240m_25m_hxtal) for system_clock_240m_25m_hxtal
    startup_gd32f450_470.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_gd32f450_470.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_gd32f450_470.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_gd32f450_470.o(RESET) refers to startup_gd32f450_470.o(STACK) for __initial_sp
    startup_gd32f450_470.o(RESET) refers to startup_gd32f450_470.o(.text) for Reset_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.NMI_Handler) for NMI_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.SVC_Handler) for SVC_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.USART0_IRQHandler) for USART0_IRQHandler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.USART1_IRQHandler) for USART1_IRQHandler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.SDIO_IRQHandler) for SDIO_IRQHandler
    startup_gd32f450_470.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_gd32f450_470.o(.text) refers to system_gd32f4xx.o(i.SystemInit) for SystemInit
    startup_gd32f450_470.o(.text) refers to __main.o(!!!main) for __main
    startup_gd32f450_470.o(.text) refers to startup_gd32f450_470.o(HEAP) for Heap_Mem
    startup_gd32f450_470.o(.text) refers to startup_gd32f450_470.o(STACK) for Stack_Mem
    gd32f4xx_adc.o(i.adc_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_adc.o(i.adc_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_can.o(i.can_debug_freeze_disable) refers to gd32f4xx_dbg.o(i.dbg_periph_disable) for dbg_periph_disable
    gd32f4xx_can.o(i.can_debug_freeze_enable) refers to gd32f4xx_dbg.o(i.dbg_periph_enable) for dbg_periph_enable
    gd32f4xx_can.o(i.can_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_can.o(i.can_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_can.o(i.can_interrupt_flag_get) refers to gd32f4xx_can.o(i.can_receive_message_length_get) for can_receive_message_length_get
    gd32f4xx_can.o(i.can_interrupt_flag_get) refers to gd32f4xx_can.o(i.can_error_get) for can_error_get
    gd32f4xx_ctc.o(i.ctc_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_ctc.o(i.ctc_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_dac.o(i.dac_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_dac.o(i.dac_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_dci.o(i.dci_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_dci.o(i.dci_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_enet.o(i.enet_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_enet.o(i.enet_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_enet.o(i.enet_deinit) refers to gd32f4xx_enet.o(i.enet_initpara_reset) for enet_initpara_reset
    gd32f4xx_enet.o(i.enet_descriptors_chain_init) refers to gd32f4xx_enet.o(.bss) for txdesc_tab
    gd32f4xx_enet.o(i.enet_descriptors_chain_init) refers to gd32f4xx_enet.o(.data) for dma_current_txdesc
    gd32f4xx_enet.o(i.enet_descriptors_ring_init) refers to gd32f4xx_enet.o(.bss) for txdesc_tab
    gd32f4xx_enet.o(i.enet_descriptors_ring_init) refers to gd32f4xx_enet.o(.data) for dma_current_txdesc
    gd32f4xx_enet.o(i.enet_disable) refers to gd32f4xx_enet.o(i.enet_tx_disable) for enet_tx_disable
    gd32f4xx_enet.o(i.enet_disable) refers to gd32f4xx_enet.o(i.enet_rx_disable) for enet_rx_disable
    gd32f4xx_enet.o(i.enet_enable) refers to gd32f4xx_enet.o(i.enet_tx_enable) for enet_tx_enable
    gd32f4xx_enet.o(i.enet_enable) refers to gd32f4xx_enet.o(i.enet_rx_enable) for enet_rx_enable
    gd32f4xx_enet.o(i.enet_frame_receive) refers to gd32f4xx_enet.o(.data) for dma_current_rxdesc
    gd32f4xx_enet.o(i.enet_frame_transmit) refers to gd32f4xx_enet.o(.data) for dma_current_txdesc
    gd32f4xx_enet.o(i.enet_init) refers to gd32f4xx_enet.o(i.enet_phy_config) for enet_phy_config
    gd32f4xx_enet.o(i.enet_init) refers to gd32f4xx_enet.o(i.enet_delay) for enet_delay
    gd32f4xx_enet.o(i.enet_init) refers to gd32f4xx_enet.o(i.enet_default_init) for enet_default_init
    gd32f4xx_enet.o(i.enet_init) refers to gd32f4xx_enet.o(i.enet_phy_write_read) for enet_phy_write_read
    gd32f4xx_enet.o(i.enet_init) refers to gd32f4xx_enet.o(.bss) for enet_initpara
    gd32f4xx_enet.o(i.enet_initpara_config) refers to gd32f4xx_enet.o(.bss) for enet_initpara
    gd32f4xx_enet.o(i.enet_initpara_reset) refers to gd32f4xx_enet.o(.bss) for enet_initpara
    gd32f4xx_enet.o(i.enet_phy_config) refers to gd32f4xx_rcu.o(i.rcu_clock_freq_get) for rcu_clock_freq_get
    gd32f4xx_enet.o(i.enet_phy_config) refers to gd32f4xx_enet.o(i.enet_phy_write_read) for enet_phy_write_read
    gd32f4xx_enet.o(i.enet_phy_config) refers to gd32f4xx_enet.o(i.enet_delay) for enet_delay
    gd32f4xx_enet.o(i.enet_phyloopback_disable) refers to gd32f4xx_enet.o(i.enet_phy_write_read) for enet_phy_write_read
    gd32f4xx_enet.o(i.enet_phyloopback_enable) refers to gd32f4xx_enet.o(i.enet_phy_write_read) for enet_phy_write_read
    gd32f4xx_enet.o(i.enet_ptp_normal_descriptors_chain_init) refers to gd32f4xx_enet.o(.bss) for txdesc_tab
    gd32f4xx_enet.o(i.enet_ptp_normal_descriptors_chain_init) refers to gd32f4xx_enet.o(.data) for dma_current_txdesc
    gd32f4xx_enet.o(i.enet_ptp_normal_descriptors_ring_init) refers to gd32f4xx_enet.o(.bss) for txdesc_tab
    gd32f4xx_enet.o(i.enet_ptp_normal_descriptors_ring_init) refers to gd32f4xx_enet.o(.data) for dma_current_txdesc
    gd32f4xx_enet.o(i.enet_ptpframe_receive_normal_mode) refers to gd32f4xx_enet.o(.data) for dma_current_rxdesc
    gd32f4xx_enet.o(i.enet_ptpframe_transmit_normal_mode) refers to gd32f4xx_enet.o(.data) for dma_current_txdesc
    gd32f4xx_enet.o(i.enet_registers_get) refers to gd32f4xx_enet.o(.constdata) for enet_reg_tab
    gd32f4xx_enet.o(i.enet_rxframe_drop) refers to gd32f4xx_enet.o(.data) for dma_current_rxdesc
    gd32f4xx_enet.o(i.enet_rxframe_size_get) refers to gd32f4xx_enet.o(i.enet_rxframe_drop) for enet_rxframe_drop
    gd32f4xx_enet.o(i.enet_rxframe_size_get) refers to gd32f4xx_enet.o(.data) for dma_current_rxdesc
    gd32f4xx_enet.o(i.enet_rxprocess_check_recovery) refers to gd32f4xx_enet.o(.data) for dma_current_rxdesc
    gd32f4xx_enet.o(i.enet_tx_disable) refers to gd32f4xx_enet.o(i.enet_txfifo_flush) for enet_txfifo_flush
    gd32f4xx_enet.o(i.enet_tx_enable) refers to gd32f4xx_enet.o(i.enet_txfifo_flush) for enet_txfifo_flush
    gd32f4xx_fmc.o(i.fmc_bank0_erase) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_bank1_erase) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_byte_program) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_halfword_program) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_mass_erase) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_page_erase) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_ready_wait) refers to gd32f4xx_fmc.o(i.fmc_state_get) for fmc_state_get
    gd32f4xx_fmc.o(i.fmc_sector_erase) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_word_program) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_drp_disable) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_drp_enable) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_erase) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_security_protection_config) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_user_write) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_write_protection_disable) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_write_protection_enable) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_gpio.o(i.gpio_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_gpio.o(i.gpio_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_i2c.o(i.i2c_clock_config) refers to gd32f4xx_rcu.o(i.rcu_clock_freq_get) for rcu_clock_freq_get
    gd32f4xx_i2c.o(i.i2c_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_i2c.o(i.i2c_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_ipa.o(i.ipa_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_ipa.o(i.ipa_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_iref.o(i.iref_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_iref.o(i.iref_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_misc.o(i.nvic_irq_enable) refers to gd32f4xx_misc.o(i.nvic_priority_group_set) for nvic_priority_group_set
    gd32f4xx_pmu.o(i.pmu_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_pmu.o(i.pmu_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_pmu.o(i.pmu_highdriver_switch_select) refers to gd32f4xx_pmu.o(i.pmu_flag_get) for pmu_flag_get
    gd32f4xx_pmu.o(i.pmu_to_deepsleepmode) refers to gd32f4xx_pmu.o(.bss) for reg_snap
    gd32f4xx_rcu.o(i.rcu_deinit) refers to gd32f4xx_rcu.o(i.rcu_osci_stab_wait) for rcu_osci_stab_wait
    gd32f4xx_rcu.o(i.rcu_osci_stab_wait) refers to gd32f4xx_rcu.o(i.rcu_flag_get) for rcu_flag_get
    gd32f4xx_rtc.o(i.rtc_coarse_calibration_config) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_coarse_calibration_config) refers to gd32f4xx_rtc.o(i.rtc_init_mode_exit) for rtc_init_mode_exit
    gd32f4xx_rtc.o(i.rtc_coarse_calibration_disable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_coarse_calibration_disable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_exit) for rtc_init_mode_exit
    gd32f4xx_rtc.o(i.rtc_coarse_calibration_enable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_coarse_calibration_enable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_exit) for rtc_init_mode_exit
    gd32f4xx_rtc.o(i.rtc_deinit) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_deinit) refers to gd32f4xx_rtc.o(i.rtc_register_sync_wait) for rtc_register_sync_wait
    gd32f4xx_rtc.o(i.rtc_init) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_init) refers to gd32f4xx_rtc.o(i.rtc_init_mode_exit) for rtc_init_mode_exit
    gd32f4xx_rtc.o(i.rtc_init) refers to gd32f4xx_rtc.o(i.rtc_register_sync_wait) for rtc_register_sync_wait
    gd32f4xx_rtc.o(i.rtc_refclock_detection_disable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_refclock_detection_disable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_exit) for rtc_init_mode_exit
    gd32f4xx_rtc.o(i.rtc_refclock_detection_enable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_refclock_detection_enable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_exit) for rtc_init_mode_exit
    gd32f4xx_rtc.o(i.rtc_second_adjust) refers to gd32f4xx_rtc.o(i.rtc_register_sync_wait) for rtc_register_sync_wait
    gd32f4xx_sdio.o(i.sdio_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_sdio.o(i.sdio_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_spi.o(i.i2s_psc_config) refers to gd32f4xx_rcu.o(i.rcu_osci_on) for rcu_osci_on
    gd32f4xx_spi.o(i.i2s_psc_config) refers to gd32f4xx_rcu.o(i.rcu_osci_stab_wait) for rcu_osci_stab_wait
    gd32f4xx_spi.o(i.i2s_psc_config) refers to gd32f4xx_rcu.o(i.rcu_i2s_clock_config) for rcu_i2s_clock_config
    gd32f4xx_spi.o(i.spi_i2s_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_spi.o(i.spi_i2s_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_syscfg.o(i.syscfg_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_syscfg.o(i.syscfg_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_timer.o(i.timer_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_timer.o(i.timer_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_timer.o(i.timer_external_clock_mode0_config) refers to gd32f4xx_timer.o(i.timer_external_trigger_config) for timer_external_trigger_config
    gd32f4xx_timer.o(i.timer_external_clock_mode1_config) refers to gd32f4xx_timer.o(i.timer_external_trigger_config) for timer_external_trigger_config
    gd32f4xx_timer.o(i.timer_external_trigger_as_external_clock_config) refers to gd32f4xx_timer.o(i.timer_input_trigger_source_select) for timer_input_trigger_source_select
    gd32f4xx_timer.o(i.timer_input_capture_config) refers to gd32f4xx_timer.o(i.timer_channel_input_capture_prescaler_config) for timer_channel_input_capture_prescaler_config
    gd32f4xx_timer.o(i.timer_input_pwm_capture_config) refers to gd32f4xx_timer.o(i.timer_channel_input_capture_prescaler_config) for timer_channel_input_capture_prescaler_config
    gd32f4xx_timer.o(i.timer_internal_trigger_as_external_clock_config) refers to gd32f4xx_timer.o(i.timer_input_trigger_source_select) for timer_input_trigger_source_select
    gd32f4xx_tli.o(i.tli_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_tli.o(i.tli_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_trng.o(i.trng_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_trng.o(i.trng_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_usart.o(i.usart_baudrate_set) refers to gd32f4xx_rcu.o(i.rcu_clock_freq_get) for rcu_clock_freq_get
    gd32f4xx_usart.o(i.usart_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_usart.o(i.usart_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_wwdgt.o(i.wwdgt_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_wwdgt.o(i.wwdgt_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    vsnprintf.o(.text) refers (Special) to _printf_a.o(.ARM.Collect$$_printf_percent$$00000006) for _printf_a
    vsnprintf.o(.text) refers (Special) to _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) for _printf_c
    vsnprintf.o(.text) refers (Special) to _printf_charcount.o(.text) for _printf_charcount
    vsnprintf.o(.text) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    vsnprintf.o(.text) refers (Special) to _printf_e.o(.ARM.Collect$$_printf_percent$$00000004) for _printf_e
    vsnprintf.o(.text) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    vsnprintf.o(.text) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    vsnprintf.o(.text) refers (Special) to printf2.o(x$fpl$printf2) for _printf_fp_hex
    vsnprintf.o(.text) refers (Special) to _printf_g.o(.ARM.Collect$$_printf_percent$$00000005) for _printf_g
    vsnprintf.o(.text) refers (Special) to _printf_i.o(.ARM.Collect$$_printf_percent$$00000008) for _printf_i
    vsnprintf.o(.text) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    vsnprintf.o(.text) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    vsnprintf.o(.text) refers (Special) to _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) for _printf_lc
    vsnprintf.o(.text) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    vsnprintf.o(.text) refers (Special) to _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) for _printf_lld
    vsnprintf.o(.text) refers (Special) to _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) for _printf_lli
    vsnprintf.o(.text) refers (Special) to _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) for _printf_llo
    vsnprintf.o(.text) refers (Special) to _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) for _printf_llu
    vsnprintf.o(.text) refers (Special) to _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) for _printf_llx
    vsnprintf.o(.text) refers (Special) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    vsnprintf.o(.text) refers (Special) to _printf_hex_int_ll_ptr.o(.text) for _printf_longlong_hex
    vsnprintf.o(.text) refers (Special) to _printf_oct_int_ll.o(.text) for _printf_longlong_oct
    vsnprintf.o(.text) refers (Special) to _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) for _printf_ls
    vsnprintf.o(.text) refers (Special) to _printf_n.o(.ARM.Collect$$_printf_percent$$00000001) for _printf_n
    vsnprintf.o(.text) refers (Special) to _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B) for _printf_o
    vsnprintf.o(.text) refers (Special) to _printf_p.o(.ARM.Collect$$_printf_percent$$00000002) for _printf_p
    vsnprintf.o(.text) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    vsnprintf.o(.text) refers (Special) to _printf_pad.o(.text) for _printf_post_padding
    vsnprintf.o(.text) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    vsnprintf.o(.text) refers (Special) to _printf_str.o(.text) for _printf_str
    vsnprintf.o(.text) refers (Special) to _printf_truncate.o(.text) for _printf_truncate_signed
    vsnprintf.o(.text) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    vsnprintf.o(.text) refers (Special) to _printf_wctomb.o(.text) for _printf_wctomb
    vsnprintf.o(.text) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    vsnprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    vsnprintf.o(.text) refers to _sputc.o(.text) for _sputc
    vsnprintf.o(.text) refers to _snputc.o(.text) for _snputc
    __2sprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    __2sprintf.o(.text) refers to _sputc.o(.text) for _sputc
    noretval__2sprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    noretval__2sprintf.o(.text) refers to _sputc.o(.text) for _sputc
    __printf.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags.o(.text) refers to __printf_flags.o(.constdata) for .constdata
    __printf_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to __printf_flags_ss.o(.constdata) for .constdata
    __printf_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_flags_wp.o(.constdata) for .constdata
    __printf_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_flags_ss_wp.o(.constdata) for .constdata
    _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) refers (Special) to _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017) for _printf_percent_end
    __0sscanf.o(.text) refers to scanf_char.o(.text) for __vfscanf_char
    __0sscanf.o(.text) refers to _sgetc.o(.text) for _sgetc
    _scanf_int.o(.text) refers to _chval.o(.text) for _chval
    rt_memcpy_v6.o(.text) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    aeabi_memset.o(.text) refers to rt_memclr.o(.text) for _memset
    rt_memclr.o(.text) refers to rt_memclr_w.o(.text) for _memset_w
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    f2d.o(x$fpl$f2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    f2d.o(x$fpl$f2d) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    f2d.o(x$fpl$f2d) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    printf1.o(x$fpl$printf1) refers to _printf_fp_dec.o(.text) for _printf_fp_dec_real
    scanf1.o(x$fpl$scanf1) refers to scanf_fp.o(.text) for _scanf_really_real
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    _printf_str.o(.text) refers (Special) to _printf_char.o(.text) for _printf_cs_common
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_dec.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_signed
    _printf_dec.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_fp_dec.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_dec.o(.text) refers (Special) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    _printf_fp_dec.o(.text) refers to bigflt0.o(.text) for _btod_etento
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_d2e) for _btod_d2e
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    _printf_fp_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_fp_dec.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_dec.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_char_common.o(.text) refers to __printf_flags_ss_wp.o(.text) for __printf
    _printf_wctomb.o(.text) refers (Special) to _printf_wchar.o(.text) for _printf_lcs_common
    _printf_wctomb.o(.text) refers to _wcrtomb.o(.text) for _wcrtomb
    _printf_wctomb.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_wctomb.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_wctomb.o(.text) refers to _printf_wctomb.o(.constdata) for .constdata
    _printf_wctomb.o(.constdata) refers (Special) to _printf_wchar.o(.text) for _printf_lcs_common
    _printf_longlong_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_longlong_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_oct_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int_ll.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll.o(.text) refers to _printf_hex_ll.o(.constdata) for .constdata
    _printf_hex_int.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int.o(.text) refers to _printf_hex_int.o(.constdata) for .constdata
    _printf_hex_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ll.o(.text) refers to _printf_hex_int_ll.o(.constdata) for .constdata
    _printf_hex_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ptr.o(.text) refers to _printf_hex_ptr.o(.constdata) for .constdata
    _printf_hex_int_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ptr.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ptr.o(.text) refers to _printf_hex_int_ptr.o(.constdata) for .constdata
    _printf_hex_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll_ptr.o(.text) refers to _printf_hex_ll_ptr.o(.constdata) for .constdata
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll_ptr.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_hex_int_ll_ptr.o(.constdata) for .constdata
    _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) refers (Weak) to _printf_char.o(.text) for _printf_char
    _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) refers (Weak) to _printf_char.o(.text) for _printf_string
    _printf_n.o(.ARM.Collect$$_printf_percent$$00000001) refers (Weak) to _printf_charcount.o(.text) for _printf_charcount
    _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_int_hex
    _printf_p.o(.ARM.Collect$$_printf_percent$$00000002) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_hex_ptr
    _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B) refers (Weak) to _printf_oct_int_ll.o(.text) for _printf_int_oct
    _printf_i.o(.ARM.Collect$$_printf_percent$$00000008) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_e.o(.ARM.Collect$$_printf_percent$$00000004) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_g.o(.ARM.Collect$$_printf_percent$$00000005) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_a.o(.ARM.Collect$$_printf_percent$$00000006) refers (Weak) to printf2.o(x$fpl$printf2) for _printf_fp_hex
    _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) refers (Weak) to _printf_wchar.o(.text) for _printf_wchar
    _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) refers (Weak) to _printf_wchar.o(.text) for _printf_wstring
    _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) refers (Weak) to _printf_oct_int_ll.o(.text) for _printf_ll_oct
    _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_ll_hex
    scanf_fp.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    scanf_fp.o(.text) refers (Special) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    scanf_fp.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    scanf_fp.o(.text) refers to istatus.o(x$fpl$ieeestatus) for __ieee_status
    scanf_fp.o(.text) refers to bigflt0.o(.text) for _btod_etento
    scanf_fp.o(.text) refers to btod.o(CL$$btod_emuld) for _btod_emuld
    scanf_fp.o(.text) refers to btod.o(CL$$btod_edivd) for _btod_edivd
    scanf_fp.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    scanf_fp.o(.text) refers to scanf2.o(x$fpl$scanf2) for _scanf_infnan
    scanf_fp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    scanf_fp.o(.text) refers to fpconst.o(c$$dmax) for __dbl_max
    scanf_fp.o(.text) refers to fpconst.o(c$$dinf) for __huge_val
    scanf_fp.o(.text) refers to narrow.o(i.__mathlib_narrow) for __mathlib_narrow
    scanf_char.o(.text) refers to _scanf.o(.text) for __vfscanf
    scanf_char.o(.text) refers to isspace.o(.text) for isspace
    dretinf.o(x$fpl$dretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fnaninf.o(x$fpl$fnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    printf2.o(x$fpl$printf2) refers to _printf_fp_hex.o(.text) for _printf_fp_hex_real
    printf2b.o(x$fpl$printf2) refers to _printf_fp_hex.o(.text) for _printf_fp_hex_real
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(i.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    rt_locale.o(.text) refers to rt_locale.o(.bss) for __rt_locale_data
    rt_locale_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    rt_errno_addr.o(.text) refers to rt_errno_addr.o(.bss) for __aeabi_errno_addr_data
    rt_errno_addr_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    isspace.o(.text) refers to rt_ctype_table.o(.text) for __rt_ctype_table
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_hex.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_hex.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_hex.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_hex.o(.text) refers to _printf_fp_hex.o(.constdata) for .constdata
    _printf_fp_hex.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_infnan.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_infnan.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_char.o(.text) refers (Weak) to _printf_str.o(.text) for _printf_str
    _printf_wchar.o(.text) refers (Weak) to _printf_wctomb.o(.text) for _printf_wctomb
    _scanf.o(.text) refers (Weak) to scanf1.o(x$fpl$scanf1) for _scanf_real
    _scanf.o(.text) refers (Weak) to _scanf_int.o(.text) for _scanf_int
    bigflt0.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    bigflt0.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    bigflt0.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    bigflt0.o(.text) refers to bigflt0.o(.constdata) for .constdata
    bigflt0.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers to btod.o(CL$$btod_d2e_norm_op1) for _d2e_norm_op1
    btod.o(CL$$btod_d2e_norm_op1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e_norm_op1) refers to btod.o(CL$$btod_d2e_denorm_low) for _d2e_denorm_low
    btod.o(CL$$btod_d2e_denorm_low) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_ediv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_emuld) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_edivd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_e2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_mult_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_div_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _wcrtomb.o(.text) refers to rt_ctype_table.o(.text) for __rt_ctype_table
    lc_numeric_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers to strcmpv7m.o(.text) for strcmp
    lc_numeric_c.o(locale$$code) refers to lc_numeric_c.o(locale$$data) for __lcnum_c_name
    fpconst.o(c$$dinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fpconst.o(c$$dnan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fpconst.o(c$$finf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fpconst.o(c$$dmax) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    istatus.o(x$fpl$ieeestatus) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    scanf2.o(x$fpl$scanf2) refers to scanf_hexfp.o(.text) for _scanf_really_hex_real
    scanf2.o(x$fpl$scanf2) refers to scanf_infnan.o(.text) for _scanf_really_infnan
    scanf2b.o(x$fpl$scanf2) refers to scanf_hexfp.o(.text) for _scanf_really_hex_real
    scanf2b.o(x$fpl$scanf2) refers to scanf_infnan.o(.text) for _scanf_really_infnan
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    narrow.o(i.__hardfp___mathlib_tofloat) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    narrow.o(i.__hardfp___mathlib_tofloat) refers to frexp.o(i.frexp) for frexp
    narrow.o(i.__hardfp___mathlib_tofloat) refers to deqf.o(x$fpl$deqf) for __aeabi_cdcmpeq
    narrow.o(i.__hardfp___mathlib_tofloat) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    narrow.o(i.__hardfp___mathlib_tofloat) refers to _rserrno.o(.text) for __set_errno
    narrow.o(i.__hardfp___mathlib_tofloat) refers to drleqf.o(x$fpl$drleqf) for __aeabi_cdrcmple
    narrow.o(i.__mathlib_narrow) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    narrow.o(i.__mathlib_narrow) refers to narrow.o(i.__hardfp___mathlib_tofloat) for __hardfp___mathlib_tofloat
    narrow.o(i.__mathlib_tofloat) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    narrow.o(i.__mathlib_tofloat) refers to narrow.o(i.__hardfp___mathlib_tofloat) for __hardfp___mathlib_tofloat
    narrow.o(i.__softfp___mathlib_tofloat) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    narrow.o(i.__softfp___mathlib_tofloat) refers to narrow.o(i.__hardfp___mathlib_tofloat) for __hardfp___mathlib_tofloat
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_gd32f450_470.o(.text) for __user_initial_stackheap
    rt_ctype_table.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    rt_ctype_table.o(.text) refers to lc_ctype_c.o(locale$$code) for _get_lc_ctype
    _rserrno.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    scanf_hexfp.o(.text) refers to _chval.o(.text) for _chval
    scanf_hexfp.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    scanf_hexfp.o(.text) refers to ldexp.o(i.__support_ldexp) for __support_ldexp
    scanf_hexfp.o(.text) refers to narrow.o(i.__mathlib_narrow) for __mathlib_narrow
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000001) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    libinit2.o(.ARM.Collect$$libinit$$00000001) refers to fpinit.o(x$fpl$fpinit) for _fp_init
    libinit2.o(.ARM.Collect$$libinit$$0000000F) refers (Weak) to rt_locale_intlibspace.o(.text) for __rt_locale
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers (Weak) to lc_ctype_c.o(locale$$code) for _get_lc_ctype
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers (Weak) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    d2f.o(x$fpl$d2f) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    d2f.o(x$fpl$d2f) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    d2f.o(x$fpl$d2f) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    deqf.o(x$fpl$deqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    deqf.o(x$fpl$deqf) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    deqf.o(x$fpl$deqf) refers to dcmpi.o(x$fpl$dcmpinf) for __fpl_dcmp_Inf
    drleqf.o(x$fpl$drleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    drleqf.o(x$fpl$drleqf) refers to dleqf.o(x$fpl$dleqf) for __fpl_dcmple_InfNaN
    frexp.o(i.__hardfp_frexp) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    frexp.o(i.__hardfp_frexp) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    frexp.o(i.__softfp_frexp) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    frexp.o(i.__softfp_frexp) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    frexp.o(i.frexp) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    frexp.o(i.frexp) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    lc_ctype_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000012) for __rt_lib_init_lc_ctype_2
    lc_ctype_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000012) for __rt_lib_init_lc_ctype_2
    lc_ctype_c.o(locale$$code) refers to strcmpv7m.o(.text) for strcmp
    lc_ctype_c.o(locale$$code) refers to lc_ctype_c.o(locale$$data) for __lcctype_c_name
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    dcmpi.o(x$fpl$dcmpinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dleqf.o(x$fpl$dleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dleqf.o(x$fpl$dleqf) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dleqf.o(x$fpl$dleqf) refers to dcmpi.o(x$fpl$dcmpinf) for __fpl_dcmp_Inf
    dmul.o(x$fpl$dmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(x$fpl$dmul) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    dmul.o(x$fpl$dmul) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dnaninf.o(x$fpl$dnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fretinf.o(x$fpl$fretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp.o(i.__hardfp_ldexp) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp.o(i.__hardfp_ldexp) refers to deqf.o(x$fpl$deqf) for __aeabi_cdcmpeq
    ldexp.o(i.__hardfp_ldexp) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    ldexp.o(i.__hardfp_ldexp) refers to _rserrno.o(.text) for __set_errno
    ldexp.o(i.__hardfp_ldexp) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    ldexp.o(i.__hardfp_ldexp) refers to dunder.o(i.__mathlib_dbl_overflow) for __mathlib_dbl_overflow
    ldexp.o(i.__softfp_ldexp) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp.o(i.__softfp_ldexp) refers to ldexp.o(i.__hardfp_ldexp) for __hardfp_ldexp
    ldexp.o(i.__support_ldexp) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp.o(i.__support_ldexp) refers to ldexp.o(i.__hardfp_ldexp) for __hardfp_ldexp
    ldexp.o(i.ldexp) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp.o(i.ldexp) refers to ldexp.o(i.__hardfp_ldexp) for __hardfp_ldexp
    ldexp_x.o(i.____hardfp_ldexp$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp_x.o(i.____hardfp_ldexp$lsc) refers to deqf.o(x$fpl$deqf) for __aeabi_cdcmpeq
    ldexp_x.o(i.____hardfp_ldexp$lsc) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    ldexp_x.o(i.____hardfp_ldexp$lsc) refers to _rserrno.o(.text) for __set_errno
    ldexp_x.o(i.____hardfp_ldexp$lsc) refers to qnan.o(.constdata) for __mathlib_zero
    ldexp_x.o(i.____softfp_ldexp$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp_x.o(i.____softfp_ldexp$lsc) refers to ldexp_x.o(i.____hardfp_ldexp$lsc) for ____hardfp_ldexp$lsc
    ldexp_x.o(i.____support_ldexp$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp_x.o(i.____support_ldexp$lsc) refers to ldexp_x.o(i.____hardfp_ldexp$lsc) for ____hardfp_ldexp$lsc
    ldexp_x.o(i.__ldexp$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp_x.o(i.__ldexp$lsc) refers to ldexp_x.o(i.____hardfp_ldexp$lsc) for ____hardfp_ldexp$lsc
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to sys_exit.o(.text) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    scalbn.o(x$fpl$scalbn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    scalbn.o(x$fpl$scalbn) refers to dcheck1.o(x$fpl$dcheck1) for __fpl_dcheck_NaN1
    dunder.o(i.__mathlib_dbl_divzero) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_infnan) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_infnan2) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_invalid) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_overflow) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_posinfnan) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_underflow) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    qnan.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_exit.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_exit.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_fini_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000009) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000011) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000012) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000006) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E) for __rt_lib_shutdown_user_alloc_1
    daddsub_clz.o(x$fpl$dadd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dadd) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dadd) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    daddsub_clz.o(x$fpl$dadd) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    daddsub_clz.o(x$fpl$drsb) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dsub) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dsub) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$dsub) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dcheck1.o(x$fpl$dcheck1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dcheck1.o(x$fpl$dcheck1) refers to retnan.o(x$fpl$retnan) for __fpl_return_NaN
    ddiv.o(x$fpl$drdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$drdiv) refers to ddiv.o(x$fpl$ddiv) for ddiv_entry
    ddiv.o(x$fpl$ddiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$ddiv) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    ddiv.o(x$fpl$ddiv) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_exit.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    retnan.o(x$fpl$retnan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    retnan.o(x$fpl$retnan) refers to trapv.o(x$fpl$trapveneer) for __fpl_cmpreturn
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    trapv.o(x$fpl$trapveneer) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display


==============================================================================

Removing Unused input sections from the image.

    Removing gd32f4xx_it.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_it.o(.revsh_text), (4 bytes).
    Removing gd32f470vet6_bsp.o(.rev16_text), (4 bytes).
    Removing gd32f470vet6_bsp.o(.revsh_text), (4 bytes).
    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing scheduler.o(.rev16_text), (4 bytes).
    Removing scheduler.o(.revsh_text), (4 bytes).
    Removing systick.o(.rev16_text), (4 bytes).
    Removing systick.o(.revsh_text), (4 bytes).
    Removing adc_app.o(.rev16_text), (4 bytes).
    Removing adc_app.o(.revsh_text), (4 bytes).
    Removing led_app.o(.rev16_text), (4 bytes).
    Removing led_app.o(.revsh_text), (4 bytes).
    Removing command_proc.o(.rev16_text), (4 bytes).
    Removing command_proc.o(.revsh_text), (4 bytes).
    Removing flash_app.o(.rev16_text), (4 bytes).
    Removing flash_app.o(.revsh_text), (4 bytes).
    Removing btn_app.o(.rev16_text), (4 bytes).
    Removing btn_app.o(.revsh_text), (4 bytes).
    Removing oled_app.o(.rev16_text), (4 bytes).
    Removing oled_app.o(.revsh_text), (4 bytes).
    Removing rtc_app.o(.rev16_text), (4 bytes).
    Removing rtc_app.o(.revsh_text), (4 bytes).
    Removing tf_app.o(.rev16_text), (4 bytes).
    Removing tf_app.o(.revsh_text), (4 bytes).
    Removing tf_app.o(i.TfCardTest), (540 bytes).
    Removing tf_app.o(i.card_info_get), (928 bytes).
    Removing tf_app.o(i.memory_compare), (36 bytes).
    Removing tf_app.o(.bss), (876 bytes).
    Removing tf_app.o(.data), (16 bytes).
    Removing usart_app.o(.rev16_text), (4 bytes).
    Removing usart_app.o(.revsh_text), (4 bytes).
    Removing ebtn.o(i.ebtn_combo_btn_remove_btn), (32 bytes).
    Removing ebtn.o(i.ebtn_combo_btn_remove_btn_by_idx), (32 bytes).
    Removing ebtn.o(i.ebtn_combo_register), (60 bytes).
    Removing ebtn.o(i.ebtn_get_btn_by_key_id), (80 bytes).
    Removing ebtn.o(i.ebtn_get_btn_index_by_btn), (12 bytes).
    Removing ebtn.o(i.ebtn_get_btn_index_by_btn_dyn), (12 bytes).
    Removing ebtn.o(i.ebtn_get_config), (12 bytes).
    Removing ebtn.o(i.ebtn_get_total_btn_cnt), (28 bytes).
    Removing ebtn.o(i.ebtn_is_btn_active), (20 bytes).
    Removing ebtn.o(i.ebtn_is_btn_in_process), (20 bytes).
    Removing ebtn.o(i.ebtn_is_in_process), (132 bytes).
    Removing ebtn.o(i.ebtn_register), (72 bytes).
    Removing oled.o(.rev16_text), (4 bytes).
    Removing oled.o(.revsh_text), (4 bytes).
    Removing oled.o(i.OLED_Allfill), (60 bytes).
    Removing oled.o(i.OLED_Display_Off), (22 bytes).
    Removing oled.o(i.OLED_Display_On), (22 bytes).
    Removing oled.o(i.OLED_Pow), (22 bytes).
    Removing oled.o(i.OLED_ShowFloat), (352 bytes).
    Removing oled.o(i.OLED_ShowHanzi), (100 bytes).
    Removing oled.o(i.OLED_ShowHzbig), (184 bytes).
    Removing oled.o(i.OLED_ShowNum), (136 bytes).
    Removing oled.o(i.OLED_ShowPic), (68 bytes).
    Removing gd25qxx.o(.rev16_text), (4 bytes).
    Removing gd25qxx.o(.revsh_text), (4 bytes).
    Removing gd25qxx.o(i.spi_flash_buffer_write), (290 bytes).
    Removing gd25qxx.o(i.spi_flash_bulk_erase), (44 bytes).
    Removing gd25qxx.o(i.spi_flash_page_write), (92 bytes).
    Removing gd25qxx.o(i.spi_flash_read_id), (84 bytes).
    Removing gd25qxx.o(i.spi_flash_sector_erase), (68 bytes).
    Removing gd25qxx.o(i.spi_flash_send_halfword_dma), (268 bytes).
    Removing gd25qxx.o(i.spi_flash_start_read_sequence), (48 bytes).
    Removing gd25qxx.o(i.spi_flash_transmit_receive_dma), (292 bytes).
    Removing gd25qxx.o(i.spi_flash_wait_for_dma_end), (44 bytes).
    Removing gd25qxx.o(i.spi_flash_wait_for_write_end), (56 bytes).
    Removing gd25qxx.o(i.spi_flash_write_enable), (36 bytes).
    Removing sdio_sdcard.o(.rev16_text), (4 bytes).
    Removing sdio_sdcard.o(.revsh_text), (4 bytes).
    Removing sdio_sdcard.o(i.sd_card_capacity_get), (168 bytes).
    Removing sdio_sdcard.o(i.sd_erase), (324 bytes).
    Removing sdio_sdcard.o(i.sd_lock_unlock), (488 bytes).
    Removing sdio_sdcard.o(i.sd_power_off), (14 bytes).
    Removing sdio_sdcard.o(i.sd_sdstatus_get), (384 bytes).
    Removing sdio_sdcard.o(i.sd_transfer_state_get), (20 bytes).
    Removing diskio.o(.rev16_text), (4 bytes).
    Removing diskio.o(.revsh_text), (4 bytes).
    Removing ff.o(i.dir_read), (190 bytes).
    Removing ff.o(i.dir_remove), (96 bytes).
    Removing ff.o(i.f_chmod), (92 bytes).
    Removing ff.o(i.f_getfree), (276 bytes).
    Removing ff.o(i.f_gets), (78 bytes).
    Removing ff.o(i.f_lseek), (432 bytes).
    Removing ff.o(i.f_mkdir), (392 bytes).
    Removing ff.o(i.f_opendir), (118 bytes).
    Removing ff.o(i.f_printf), (700 bytes).
    Removing ff.o(i.f_putc), (38 bytes).
    Removing ff.o(i.f_puts), (42 bytes).
    Removing ff.o(i.f_readdir), (100 bytes).
    Removing ff.o(i.f_rename), (298 bytes).
    Removing ff.o(i.f_stat), (66 bytes).
    Removing ff.o(i.f_truncate), (156 bytes).
    Removing ff.o(i.f_unlink), (186 bytes).
    Removing ff.o(i.f_utime), (94 bytes).
    Removing ff.o(i.get_fileinfo), (316 bytes).
    Removing ff.o(i.pick_lfn), (116 bytes).
    Removing gd30ad3344.o(.rev16_text), (4 bytes).
    Removing gd30ad3344.o(.revsh_text), (4 bytes).
    Removing gd30ad3344.o(i.ad3344_Exit_disable), (2 bytes).
    Removing gd30ad3344.o(i.ad3344_Exit_enable), (2 bytes).
    Removing gd30ad3344.o(i.ad3344_read_data32), (26 bytes).
    Removing gd30ad3344.o(i.ad3344_read_regs), (72 bytes).
    Removing gd30ad3344.o(i.ad3344_stop_conver), (28 bytes).
    Removing system_gd32f4xx.o(.rev16_text), (4 bytes).
    Removing system_gd32f4xx.o(.revsh_text), (4 bytes).
    Removing system_gd32f4xx.o(i.SystemCoreClockUpdate), (272 bytes).
    Removing system_gd32f4xx.o(i.gd32f4xx_firmware_version_get), (6 bytes).
    Removing gd32f4xx_adc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_adc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_adc.o(i.adc_channel_16_to_18), (96 bytes).
    Removing gd32f4xx_adc.o(i.adc_deinit), (20 bytes).
    Removing gd32f4xx_adc.o(i.adc_disable), (10 bytes).
    Removing gd32f4xx_adc.o(i.adc_discontinuous_mode_config), (82 bytes).
    Removing gd32f4xx_adc.o(i.adc_dma_mode_disable), (10 bytes).
    Removing gd32f4xx_adc.o(i.adc_dma_request_after_last_disable), (10 bytes).
    Removing gd32f4xx_adc.o(i.adc_end_of_conversion_config), (34 bytes).
    Removing gd32f4xx_adc.o(i.adc_flag_clear), (8 bytes).
    Removing gd32f4xx_adc.o(i.adc_flag_get), (14 bytes).
    Removing gd32f4xx_adc.o(i.adc_inserted_channel_config), (124 bytes).
    Removing gd32f4xx_adc.o(i.adc_inserted_channel_offset_config), (36 bytes).
    Removing gd32f4xx_adc.o(i.adc_inserted_data_read), (46 bytes).
    Removing gd32f4xx_adc.o(i.adc_inserted_software_startconv_flag_get), (16 bytes).
    Removing gd32f4xx_adc.o(i.adc_interrupt_disable), (66 bytes).
    Removing gd32f4xx_adc.o(i.adc_interrupt_enable), (66 bytes).
    Removing gd32f4xx_adc.o(i.adc_interrupt_flag_clear), (8 bytes).
    Removing gd32f4xx_adc.o(i.adc_interrupt_flag_get), (112 bytes).
    Removing gd32f4xx_adc.o(i.adc_oversample_mode_config), (58 bytes).
    Removing gd32f4xx_adc.o(i.adc_oversample_mode_disable), (14 bytes).
    Removing gd32f4xx_adc.o(i.adc_oversample_mode_enable), (14 bytes).
    Removing gd32f4xx_adc.o(i.adc_resolution_config), (16 bytes).
    Removing gd32f4xx_adc.o(i.adc_routine_data_read), (8 bytes).
    Removing gd32f4xx_adc.o(i.adc_routine_software_startconv_flag_get), (16 bytes).
    Removing gd32f4xx_adc.o(i.adc_sync_delay_config), (36 bytes).
    Removing gd32f4xx_adc.o(i.adc_sync_dma_config), (36 bytes).
    Removing gd32f4xx_adc.o(i.adc_sync_dma_request_after_last_disable), (24 bytes).
    Removing gd32f4xx_adc.o(i.adc_sync_dma_request_after_last_enable), (24 bytes).
    Removing gd32f4xx_adc.o(i.adc_sync_routine_data_read), (12 bytes).
    Removing gd32f4xx_adc.o(i.adc_watchdog_disable), (50 bytes).
    Removing gd32f4xx_adc.o(i.adc_watchdog_sequence_channel_enable), (64 bytes).
    Removing gd32f4xx_adc.o(i.adc_watchdog_single_channel_disable), (10 bytes).
    Removing gd32f4xx_adc.o(i.adc_watchdog_single_channel_enable), (24 bytes).
    Removing gd32f4xx_adc.o(i.adc_watchdog_threshold_config), (14 bytes).
    Removing gd32f4xx_can.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_can.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_can.o(i.can1_filter_start_bank), (56 bytes).
    Removing gd32f4xx_can.o(i.can_debug_freeze_disable), (44 bytes).
    Removing gd32f4xx_can.o(i.can_debug_freeze_enable), (44 bytes).
    Removing gd32f4xx_can.o(i.can_deinit), (52 bytes).
    Removing gd32f4xx_can.o(i.can_error_get), (12 bytes).
    Removing gd32f4xx_can.o(i.can_fifo_release), (32 bytes).
    Removing gd32f4xx_can.o(i.can_filter_init), (272 bytes).
    Removing gd32f4xx_can.o(i.can_flag_clear), (16 bytes).
    Removing gd32f4xx_can.o(i.can_flag_get), (30 bytes).
    Removing gd32f4xx_can.o(i.can_init), (290 bytes).
    Removing gd32f4xx_can.o(i.can_interrupt_disable), (8 bytes).
    Removing gd32f4xx_can.o(i.can_interrupt_enable), (8 bytes).
    Removing gd32f4xx_can.o(i.can_interrupt_flag_clear), (16 bytes).
    Removing gd32f4xx_can.o(i.can_interrupt_flag_get), (116 bytes).
    Removing gd32f4xx_can.o(i.can_message_receive), (228 bytes).
    Removing gd32f4xx_can.o(i.can_message_transmit), (336 bytes).
    Removing gd32f4xx_can.o(i.can_receive_error_number_get), (8 bytes).
    Removing gd32f4xx_can.o(i.can_receive_message_length_get), (26 bytes).
    Removing gd32f4xx_can.o(i.can_struct_para_init), (164 bytes).
    Removing gd32f4xx_can.o(i.can_time_trigger_mode_disable), (48 bytes).
    Removing gd32f4xx_can.o(i.can_time_trigger_mode_enable), (48 bytes).
    Removing gd32f4xx_can.o(i.can_transmission_stop), (80 bytes).
    Removing gd32f4xx_can.o(i.can_transmit_error_number_get), (10 bytes).
    Removing gd32f4xx_can.o(i.can_transmit_states), (124 bytes).
    Removing gd32f4xx_can.o(i.can_wakeup), (48 bytes).
    Removing gd32f4xx_can.o(i.can_working_mode_set), (168 bytes).
    Removing gd32f4xx_crc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_crc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_crc.o(i.crc_block_data_calculate), (36 bytes).
    Removing gd32f4xx_crc.o(i.crc_data_register_read), (12 bytes).
    Removing gd32f4xx_crc.o(i.crc_data_register_reset), (20 bytes).
    Removing gd32f4xx_crc.o(i.crc_deinit), (24 bytes).
    Removing gd32f4xx_crc.o(i.crc_free_data_register_read), (12 bytes).
    Removing gd32f4xx_crc.o(i.crc_free_data_register_write), (12 bytes).
    Removing gd32f4xx_crc.o(i.crc_single_data_calculate), (16 bytes).
    Removing gd32f4xx_ctc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_ctc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_clock_limit_value_config), (28 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_counter_capture_value_read), (16 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_counter_direction_read), (24 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_counter_disable), (20 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_counter_enable), (20 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_counter_reload_value_config), (28 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_counter_reload_value_read), (16 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_deinit), (20 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_flag_clear), (36 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_flag_get), (24 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_hardware_trim_mode_config), (28 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_interrupt_disable), (16 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_interrupt_enable), (16 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_interrupt_flag_clear), (36 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_interrupt_flag_get), (56 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_irc48m_trim_value_config), (28 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_irc48m_trim_value_read), (16 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_refsource_polarity_config), (28 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_refsource_prescaler_config), (28 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_refsource_signal_select), (28 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_software_refsource_pulse_generate), (20 bytes).
    Removing gd32f4xx_dac.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_dac.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_dac.o(i.dac_concurrent_data_set), (48 bytes).
    Removing gd32f4xx_dac.o(i.dac_concurrent_disable), (14 bytes).
    Removing gd32f4xx_dac.o(i.dac_concurrent_enable), (14 bytes).
    Removing gd32f4xx_dac.o(i.dac_concurrent_output_buffer_disable), (14 bytes).
    Removing gd32f4xx_dac.o(i.dac_concurrent_output_buffer_enable), (14 bytes).
    Removing gd32f4xx_dac.o(i.dac_concurrent_software_trigger_enable), (12 bytes).
    Removing gd32f4xx_dac.o(i.dac_data_set), (64 bytes).
    Removing gd32f4xx_dac.o(i.dac_deinit), (40 bytes).
    Removing gd32f4xx_dac.o(i.dac_disable), (26 bytes).
    Removing gd32f4xx_dac.o(i.dac_dma_disable), (26 bytes).
    Removing gd32f4xx_dac.o(i.dac_dma_enable), (26 bytes).
    Removing gd32f4xx_dac.o(i.dac_enable), (26 bytes).
    Removing gd32f4xx_dac.o(i.dac_flag_clear), (14 bytes).
    Removing gd32f4xx_dac.o(i.dac_flag_get), (26 bytes).
    Removing gd32f4xx_dac.o(i.dac_interrupt_disable), (18 bytes).
    Removing gd32f4xx_dac.o(i.dac_interrupt_enable), (18 bytes).
    Removing gd32f4xx_dac.o(i.dac_interrupt_flag_clear), (14 bytes).
    Removing gd32f4xx_dac.o(i.dac_interrupt_flag_get), (38 bytes).
    Removing gd32f4xx_dac.o(i.dac_lfsr_noise_config), (40 bytes).
    Removing gd32f4xx_dac.o(i.dac_output_buffer_disable), (26 bytes).
    Removing gd32f4xx_dac.o(i.dac_output_buffer_enable), (26 bytes).
    Removing gd32f4xx_dac.o(i.dac_output_value_get), (22 bytes).
    Removing gd32f4xx_dac.o(i.dac_software_trigger_enable), (26 bytes).
    Removing gd32f4xx_dac.o(i.dac_triangle_noise_config), (40 bytes).
    Removing gd32f4xx_dac.o(i.dac_trigger_disable), (26 bytes).
    Removing gd32f4xx_dac.o(i.dac_trigger_enable), (26 bytes).
    Removing gd32f4xx_dac.o(i.dac_trigger_source_config), (40 bytes).
    Removing gd32f4xx_dac.o(i.dac_wave_mode_config), (40 bytes).
    Removing gd32f4xx_dbg.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_dbg.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_deinit), (16 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_id_get), (12 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_low_power_disable), (16 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_low_power_enable), (16 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_periph_disable), (32 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_periph_enable), (32 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_trace_pin_disable), (20 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_trace_pin_enable), (20 bytes).
    Removing gd32f4xx_dci.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_dci.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_dci.o(i.dci_capture_disable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_capture_enable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_crop_window_config), (24 bytes).
    Removing gd32f4xx_dci.o(i.dci_crop_window_disable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_crop_window_enable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_data_read), (12 bytes).
    Removing gd32f4xx_dci.o(i.dci_deinit), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_disable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_embedded_sync_disable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_embedded_sync_enable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_enable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_flag_get), (36 bytes).
    Removing gd32f4xx_dci.o(i.dci_init), (52 bytes).
    Removing gd32f4xx_dci.o(i.dci_interrupt_disable), (16 bytes).
    Removing gd32f4xx_dci.o(i.dci_interrupt_enable), (16 bytes).
    Removing gd32f4xx_dci.o(i.dci_interrupt_flag_clear), (16 bytes).
    Removing gd32f4xx_dci.o(i.dci_interrupt_flag_get), (24 bytes).
    Removing gd32f4xx_dci.o(i.dci_jpeg_disable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_jpeg_enable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_sync_codes_config), (24 bytes).
    Removing gd32f4xx_dci.o(i.dci_sync_codes_unmask_config), (24 bytes).
    Removing gd32f4xx_dma.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_dma.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_dma.o(i.dma_fifo_status_get), (20 bytes).
    Removing gd32f4xx_dma.o(i.dma_interrupt_disable), (62 bytes).
    Removing gd32f4xx_dma.o(i.dma_interrupt_enable), (62 bytes).
    Removing gd32f4xx_dma.o(i.dma_interrupt_flag_clear), (62 bytes).
    Removing gd32f4xx_dma.o(i.dma_interrupt_flag_get), (516 bytes).
    Removing gd32f4xx_dma.o(i.dma_memory_address_generation_config), (64 bytes).
    Removing gd32f4xx_dma.o(i.dma_memory_burst_beats_config), (36 bytes).
    Removing gd32f4xx_dma.o(i.dma_memory_width_config), (36 bytes).
    Removing gd32f4xx_dma.o(i.dma_multi_data_para_struct_init), (40 bytes).
    Removing gd32f4xx_dma.o(i.dma_periph_address_config), (16 bytes).
    Removing gd32f4xx_dma.o(i.dma_periph_burst_beats_config), (36 bytes).
    Removing gd32f4xx_dma.o(i.dma_periph_width_config), (36 bytes).
    Removing gd32f4xx_dma.o(i.dma_peripheral_address_generation_config), (126 bytes).
    Removing gd32f4xx_dma.o(i.dma_priority_config), (36 bytes).
    Removing gd32f4xx_dma.o(i.dma_switch_buffer_mode_config), (76 bytes).
    Removing gd32f4xx_dma.o(i.dma_switch_buffer_mode_enable), (66 bytes).
    Removing gd32f4xx_dma.o(i.dma_transfer_direction_config), (36 bytes).
    Removing gd32f4xx_dma.o(i.dma_using_memory_get), (28 bytes).
    Removing gd32f4xx_enet.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_enet.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_enet.o(i.enet_address_filter_config), (32 bytes).
    Removing gd32f4xx_enet.o(i.enet_address_filter_disable), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_address_filter_enable), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_current_desc_address_get), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_debug_status_get), (108 bytes).
    Removing gd32f4xx_enet.o(i.enet_default_init), (152 bytes).
    Removing gd32f4xx_enet.o(i.enet_deinit), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_delay), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_desc_flag_clear), (8 bytes).
    Removing gd32f4xx_enet.o(i.enet_desc_flag_get), (14 bytes).
    Removing gd32f4xx_enet.o(i.enet_desc_flag_set), (8 bytes).
    Removing gd32f4xx_enet.o(i.enet_desc_information_get), (100 bytes).
    Removing gd32f4xx_enet.o(i.enet_desc_select_normal_mode), (20 bytes).
    Removing gd32f4xx_enet.o(i.enet_descriptors_chain_init), (200 bytes).
    Removing gd32f4xx_enet.o(i.enet_descriptors_ring_init), (236 bytes).
    Removing gd32f4xx_enet.o(i.enet_disable), (12 bytes).
    Removing gd32f4xx_enet.o(i.enet_dma_feature_disable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_dma_feature_enable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_dmaprocess_resume), (28 bytes).
    Removing gd32f4xx_enet.o(i.enet_dmaprocess_state_get), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_enable), (12 bytes).
    Removing gd32f4xx_enet.o(i.enet_flag_clear), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_flag_get), (36 bytes).
    Removing gd32f4xx_enet.o(i.enet_fliter_feature_disable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_fliter_feature_enable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_flowcontrol_feature_disable), (40 bytes).
    Removing gd32f4xx_enet.o(i.enet_flowcontrol_feature_enable), (40 bytes).
    Removing gd32f4xx_enet.o(i.enet_flowcontrol_threshold_config), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_forward_feature_disable), (48 bytes).
    Removing gd32f4xx_enet.o(i.enet_forward_feature_enable), (48 bytes).
    Removing gd32f4xx_enet.o(i.enet_frame_receive), (248 bytes).
    Removing gd32f4xx_enet.o(i.enet_frame_transmit), (204 bytes).
    Removing gd32f4xx_enet.o(i.enet_init), (868 bytes).
    Removing gd32f4xx_enet.o(i.enet_initpara_config), (356 bytes).
    Removing gd32f4xx_enet.o(i.enet_initpara_reset), (40 bytes).
    Removing gd32f4xx_enet.o(i.enet_interrupt_disable), (72 bytes).
    Removing gd32f4xx_enet.o(i.enet_interrupt_enable), (72 bytes).
    Removing gd32f4xx_enet.o(i.enet_interrupt_flag_clear), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_interrupt_flag_get), (36 bytes).
    Removing gd32f4xx_enet.o(i.enet_mac_address_get), (60 bytes).
    Removing gd32f4xx_enet.o(i.enet_mac_address_set), (48 bytes).
    Removing gd32f4xx_enet.o(i.enet_missed_frame_counter_get), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_msc_counters_get), (12 bytes).
    Removing gd32f4xx_enet.o(i.enet_msc_counters_preset_config), (36 bytes).
    Removing gd32f4xx_enet.o(i.enet_msc_counters_reset), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_msc_feature_disable), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_msc_feature_enable), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_pauseframe_config), (44 bytes).
    Removing gd32f4xx_enet.o(i.enet_pauseframe_detect_config), (28 bytes).
    Removing gd32f4xx_enet.o(i.enet_pauseframe_generate), (36 bytes).
    Removing gd32f4xx_enet.o(i.enet_phy_config), (216 bytes).
    Removing gd32f4xx_enet.o(i.enet_phy_write_read), (156 bytes).
    Removing gd32f4xx_enet.o(i.enet_phyloopback_disable), (50 bytes).
    Removing gd32f4xx_enet.o(i.enet_phyloopback_enable), (48 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_expected_time_config), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_feature_disable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_feature_enable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_normal_descriptors_chain_init), (236 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_normal_descriptors_ring_init), (280 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_pps_output_frequency_config), (12 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_subsecond_increment_config), (12 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_system_time_get), (32 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_timestamp_addend_config), (12 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_timestamp_function_config), (256 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_timestamp_update_config), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptpframe_receive_normal_mode), (340 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptpframe_transmit_normal_mode), (444 bytes).
    Removing gd32f4xx_enet.o(i.enet_registers_get), (56 bytes).
    Removing gd32f4xx_enet.o(i.enet_rx_desc_delay_receive_complete_interrupt), (20 bytes).
    Removing gd32f4xx_enet.o(i.enet_rx_desc_immediate_receive_complete_interrupt), (10 bytes).
    Removing gd32f4xx_enet.o(i.enet_rx_disable), (36 bytes).
    Removing gd32f4xx_enet.o(i.enet_rx_enable), (36 bytes).
    Removing gd32f4xx_enet.o(i.enet_rxframe_drop), (172 bytes).
    Removing gd32f4xx_enet.o(i.enet_rxframe_size_get), (152 bytes).
    Removing gd32f4xx_enet.o(i.enet_rxprocess_check_recovery), (48 bytes).
    Removing gd32f4xx_enet.o(i.enet_software_reset), (60 bytes).
    Removing gd32f4xx_enet.o(i.enet_transmit_checksum_config), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_tx_disable), (40 bytes).
    Removing gd32f4xx_enet.o(i.enet_tx_enable), (40 bytes).
    Removing gd32f4xx_enet.o(i.enet_txfifo_flush), (52 bytes).
    Removing gd32f4xx_enet.o(i.enet_wum_feature_disable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_wum_feature_enable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_wum_filter_config), (28 bytes).
    Removing gd32f4xx_enet.o(i.enet_wum_filter_register_pointer_reset), (20 bytes).
    Removing gd32f4xx_enet.o(.bss), (15460 bytes).
    Removing gd32f4xx_enet.o(.constdata), (116 bytes).
    Removing gd32f4xx_enet.o(.data), (20 bytes).
    Removing gd32f4xx_exmc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_exmc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_ecc_get), (12 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_flag_clear), (52 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_flag_get), (52 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_interrupt_disable), (56 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_interrupt_enable), (56 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_interrupt_flag_clear), (56 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_interrupt_flag_get), (72 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_nand_deinit), (42 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_nand_disable), (22 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_nand_ecc_config), (48 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_nand_enable), (22 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_nand_init), (172 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_nand_struct_para_init), (54 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_norsram_consecutive_clock_config), (42 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_norsram_deinit), (36 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_norsram_disable), (22 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_norsram_enable), (22 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_norsram_init), (228 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_norsram_page_size_config), (40 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_norsram_struct_para_init), (106 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_pccard_deinit), (36 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_pccard_disable), (24 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_pccard_enable), (24 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_pccard_init), (188 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_pccard_struct_para_init), (60 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_autorefresh_number_set), (32 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_bankstatus_get), (32 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_command_config), (28 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_deinit), (56 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_init), (284 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_readsample_config), (32 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_readsample_enable), (44 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_refresh_count_set), (36 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_struct_command_para_init), (16 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_struct_para_init), (66 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_write_protection_config), (64 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_deinit), (44 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_high_id_get), (12 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_init), (24 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_low_id_get), (12 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_read_command_set), (28 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_read_id_command_send), (24 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_send_command_state_get), (48 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_struct_para_init), (20 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_write_cmd_send), (24 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_write_command_set), (28 bytes).
    Removing gd32f4xx_exti.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_exti.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_exti.o(i.exti_deinit), (28 bytes).
    Removing gd32f4xx_exti.o(i.exti_event_disable), (16 bytes).
    Removing gd32f4xx_exti.o(i.exti_event_enable), (16 bytes).
    Removing gd32f4xx_exti.o(i.exti_flag_clear), (12 bytes).
    Removing gd32f4xx_exti.o(i.exti_flag_get), (24 bytes).
    Removing gd32f4xx_exti.o(i.exti_init), (188 bytes).
    Removing gd32f4xx_exti.o(i.exti_interrupt_disable), (16 bytes).
    Removing gd32f4xx_exti.o(i.exti_interrupt_enable), (16 bytes).
    Removing gd32f4xx_exti.o(i.exti_interrupt_flag_clear), (12 bytes).
    Removing gd32f4xx_exti.o(i.exti_interrupt_flag_get), (24 bytes).
    Removing gd32f4xx_exti.o(i.exti_software_interrupt_disable), (16 bytes).
    Removing gd32f4xx_exti.o(i.exti_software_interrupt_enable), (16 bytes).
    Removing gd32f4xx_fmc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_fmc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_bank0_erase), (68 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_bank1_erase), (68 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_byte_program), (80 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_flag_clear), (12 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_flag_get), (24 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_halfword_program), (84 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_interrupt_disable), (16 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_interrupt_enable), (16 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_interrupt_flag_clear), (12 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_interrupt_flag_get), (64 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_lock), (20 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_mass_erase), (72 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_page_erase), (124 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_ready_wait), (32 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_sector_erase), (96 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_state_get), (76 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_unlock), (36 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_word_program), (84 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_wscnt_set), (24 bytes).
    Removing gd32f4xx_fmc.o(i.ob_boot_mode_config), (24 bytes).
    Removing gd32f4xx_fmc.o(i.ob_double_bank_select), (24 bytes).
    Removing gd32f4xx_fmc.o(i.ob_drp0_get), (32 bytes).
    Removing gd32f4xx_fmc.o(i.ob_drp1_get), (32 bytes).
    Removing gd32f4xx_fmc.o(i.ob_drp_disable), (96 bytes).
    Removing gd32f4xx_fmc.o(i.ob_drp_enable), (84 bytes).
    Removing gd32f4xx_fmc.o(i.ob_erase), (76 bytes).
    Removing gd32f4xx_fmc.o(i.ob_lock), (20 bytes).
    Removing gd32f4xx_fmc.o(i.ob_security_protection_config), (40 bytes).
    Removing gd32f4xx_fmc.o(i.ob_spc_get), (28 bytes).
    Removing gd32f4xx_fmc.o(i.ob_start), (20 bytes).
    Removing gd32f4xx_fmc.o(i.ob_unlock), (36 bytes).
    Removing gd32f4xx_fmc.o(i.ob_user_bor_threshold), (24 bytes).
    Removing gd32f4xx_fmc.o(i.ob_user_bor_threshold_get), (16 bytes).
    Removing gd32f4xx_fmc.o(i.ob_user_get), (16 bytes).
    Removing gd32f4xx_fmc.o(i.ob_user_write), (52 bytes).
    Removing gd32f4xx_fmc.o(i.ob_write_protection0_get), (16 bytes).
    Removing gd32f4xx_fmc.o(i.ob_write_protection1_get), (16 bytes).
    Removing gd32f4xx_fmc.o(i.ob_write_protection_disable), (72 bytes).
    Removing gd32f4xx_fmc.o(i.ob_write_protection_enable), (72 bytes).
    Removing gd32f4xx_fwdgt.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_fwdgt.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_fwdgt.o(i.fwdgt_config), (104 bytes).
    Removing gd32f4xx_fwdgt.o(i.fwdgt_counter_reload), (16 bytes).
    Removing gd32f4xx_fwdgt.o(i.fwdgt_enable), (16 bytes).
    Removing gd32f4xx_fwdgt.o(i.fwdgt_flag_get), (24 bytes).
    Removing gd32f4xx_fwdgt.o(i.fwdgt_prescaler_value_config), (60 bytes).
    Removing gd32f4xx_fwdgt.o(i.fwdgt_reload_value_config), (64 bytes).
    Removing gd32f4xx_fwdgt.o(i.fwdgt_write_disable), (12 bytes).
    Removing gd32f4xx_fwdgt.o(i.fwdgt_write_enable), (16 bytes).
    Removing gd32f4xx_gpio.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_gpio.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_bit_toggle), (4 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_deinit), (206 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_input_port_get), (8 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_output_bit_get), (16 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_output_port_get), (8 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_pin_lock), (18 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_port_toggle), (8 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_port_write), (4 bytes).
    Removing gd32f4xx_i2c.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_i2c.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_ackpos_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_analog_noise_filter_disable), (10 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_analog_noise_filter_enable), (10 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_data_receive), (8 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_data_transmit), (6 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_digital_noise_filter_config), (8 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_disable), (10 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_dma_last_transfer_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_dualaddr_disable), (10 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_dualaddr_enable), (12 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_interrupt_disable), (26 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_interrupt_enable), (26 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_interrupt_flag_clear), (44 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_interrupt_flag_get), (92 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_pec_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_pec_transfer_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_pec_value_get), (10 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_sam_disable), (14 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_sam_enable), (14 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_sam_timeout_disable), (14 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_sam_timeout_enable), (14 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_slave_response_to_gcall_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_smbus_alert_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_smbus_arp_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_smbus_type_config), (24 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_software_reset_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_stretch_scl_low_config), (16 bytes).
    Removing gd32f4xx_ipa.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_ipa.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_background_init), (164 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_background_lut_init), (100 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_background_lut_loading_enable), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_background_struct_para_init), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_deinit), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_destination_init), (316 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_destination_struct_para_init), (22 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_flag_clear), (16 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_flag_get), (24 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_foreground_init), (164 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_foreground_lut_init), (100 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_foreground_lut_loading_enable), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_foreground_struct_para_init), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_inter_timer_config), (36 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_interrupt_disable), (16 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_interrupt_enable), (16 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_interrupt_flag_clear), (16 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_interrupt_flag_get), (24 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_interval_clock_num_config), (28 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_line_mark_config), (24 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_pixel_format_convert_mode_set), (28 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_transfer_enable), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_transfer_hangup_disable), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_transfer_hangup_enable), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_transfer_stop_disable), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_transfer_stop_enable), (20 bytes).
    Removing gd32f4xx_iref.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_iref.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_iref.o(i.iref_deinit), (20 bytes).
    Removing gd32f4xx_iref.o(i.iref_disable), (20 bytes).
    Removing gd32f4xx_iref.o(i.iref_enable), (20 bytes).
    Removing gd32f4xx_iref.o(i.iref_mode_set), (28 bytes).
    Removing gd32f4xx_iref.o(i.iref_precision_trim_value_set), (28 bytes).
    Removing gd32f4xx_iref.o(i.iref_sink_set), (28 bytes).
    Removing gd32f4xx_iref.o(i.iref_step_data_config), (28 bytes).
    Removing gd32f4xx_misc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_misc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_misc.o(i.nvic_irq_disable), (24 bytes).
    Removing gd32f4xx_misc.o(i.nvic_vector_table_set), (24 bytes).
    Removing gd32f4xx_misc.o(i.system_lowpower_reset), (16 bytes).
    Removing gd32f4xx_misc.o(i.system_lowpower_set), (16 bytes).
    Removing gd32f4xx_misc.o(i.systick_clksource_set), (40 bytes).
    Removing gd32f4xx_pmu.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_pmu.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_backup_ldo_config), (28 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_backup_write_disable), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_deinit), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_flag_clear), (48 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_flag_get), (24 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_highdriver_mode_disable), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_highdriver_mode_enable), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_highdriver_switch_select), (44 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_ldo_output_select), (28 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_lowdriver_mode_disable), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_lowdriver_mode_enable), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_lowpower_driver_config), (28 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_lvd_disable), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_lvd_select), (48 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_normalpower_driver_config), (28 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_to_deepsleepmode), (244 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_to_sleepmode), (28 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_to_standbymode), (108 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_wakeup_pin_disable), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_wakeup_pin_enable), (20 bytes).
    Removing gd32f4xx_pmu.o(.bss), (16 bytes).
    Removing gd32f4xx_rcu.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_rcu.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_ahb_clock_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_apb1_clock_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_apb2_clock_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_bkp_reset_disable), (20 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_bkp_reset_enable), (20 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_ck48m_clock_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_ckout0_config), (28 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_ckout1_config), (28 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_deepsleep_voltage_set), (16 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_deinit), (140 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_hxtal_clock_monitor_disable), (20 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_hxtal_clock_monitor_enable), (20 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_i2s_clock_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_interrupt_disable), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_interrupt_enable), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_interrupt_flag_clear), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_interrupt_flag_get), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_irc16m_adjust_value_set), (28 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_lxtal_drive_capability_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_osci_bypass_mode_disable), (116 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_osci_bypass_mode_enable), (116 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_osci_off), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_periph_clock_disable), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_periph_clock_sleep_disable), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_periph_clock_sleep_enable), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_pll48m_clock_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_pll_config), (132 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_plli2s_config), (44 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_pllsai_config), (72 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_rtc_div_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_spread_spectrum_config), (32 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_spread_spectrum_disable), (20 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_spread_spectrum_enable), (20 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_system_clock_source_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_system_clock_source_get), (16 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_timer_clock_prescaler_config), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_tli_clock_div_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_voltage_key_unlock), (16 bytes).
    Removing gd32f4xx_rtc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_rtc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_alarm_config), (100 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_alarm_disable), (128 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_alarm_enable), (60 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_alarm_get), (68 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_alarm_output_config), (84 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_alarm_subsecond_config), (52 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_alarm_subsecond_get), (32 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_bypass_shadow_disable), (40 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_bypass_shadow_enable), (40 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_calibration_output_config), (48 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_coarse_calibration_config), (116 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_coarse_calibration_disable), (56 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_coarse_calibration_enable), (56 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_deinit), (204 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_flag_clear), (16 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_flag_get), (20 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_hour_adjust), (36 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_interrupt_disable), (60 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_interrupt_enable), (60 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_refclock_detection_disable), (56 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_refclock_detection_enable), (56 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_second_adjust), (108 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_smooth_calibration_config), (80 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_subsecond_get), (20 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_tamper0_pin_map), (28 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_tamper_disable), (16 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_tamper_enable), (200 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_timestamp_disable), (40 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_timestamp_enable), (48 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_timestamp_get), (60 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_timestamp_pin_map), (28 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_timestamp_subsecond_get), (12 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_wakeup_clock_set), (92 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_wakeup_disable), (84 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_wakeup_enable), (40 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_wakeup_timer_get), (12 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_wakeup_timer_set), (76 bytes).
    Removing gd32f4xx_sdio.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_sdio.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_ceata_command_completion_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_ceata_command_completion_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_ceata_command_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_ceata_command_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_ceata_interrupt_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_ceata_interrupt_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_clock_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_csm_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_data_counter_get), (12 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_fifo_counter_get), (12 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_hardware_clock_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_operation_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_operation_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_readwait_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_readwait_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_readwait_type_set), (40 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_stop_readwait_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_stop_readwait_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_suspend_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_suspend_enable), (20 bytes).
    Removing gd32f4xx_spi.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_spi.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_spi.o(i.i2s_disable), (10 bytes).
    Removing gd32f4xx_spi.o(i.i2s_enable), (10 bytes).
    Removing gd32f4xx_spi.o(i.i2s_full_duplex_mode_config), (48 bytes).
    Removing gd32f4xx_spi.o(i.i2s_init), (28 bytes).
    Removing gd32f4xx_spi.o(i.i2s_psc_config), (292 bytes).
    Removing gd32f4xx_spi.o(i.spi_bidirectional_transfer_config), (26 bytes).
    Removing gd32f4xx_spi.o(i.spi_crc_error_clear), (8 bytes).
    Removing gd32f4xx_spi.o(i.spi_crc_get), (16 bytes).
    Removing gd32f4xx_spi.o(i.spi_crc_next), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_crc_off), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_crc_on), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_crc_polynomial_get), (8 bytes).
    Removing gd32f4xx_spi.o(i.spi_crc_polynomial_set), (4 bytes).
    Removing gd32f4xx_spi.o(i.spi_disable), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_i2s_data_frame_format_config), (16 bytes).
    Removing gd32f4xx_spi.o(i.spi_i2s_deinit), (172 bytes).
    Removing gd32f4xx_spi.o(i.spi_i2s_format_error_clear), (6 bytes).
    Removing gd32f4xx_spi.o(i.spi_i2s_interrupt_disable), (48 bytes).
    Removing gd32f4xx_spi.o(i.spi_i2s_interrupt_enable), (48 bytes).
    Removing gd32f4xx_spi.o(i.spi_i2s_interrupt_flag_get), (112 bytes).
    Removing gd32f4xx_spi.o(i.spi_nss_internal_high), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_nss_internal_low), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_nss_output_disable), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_nss_output_enable), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_quad_disable), (14 bytes).
    Removing gd32f4xx_spi.o(i.spi_quad_enable), (14 bytes).
    Removing gd32f4xx_spi.o(i.spi_quad_io23_output_disable), (14 bytes).
    Removing gd32f4xx_spi.o(i.spi_quad_io23_output_enable), (14 bytes).
    Removing gd32f4xx_spi.o(i.spi_quad_read_enable), (14 bytes).
    Removing gd32f4xx_spi.o(i.spi_quad_write_enable), (14 bytes).
    Removing gd32f4xx_spi.o(i.spi_struct_para_init), (18 bytes).
    Removing gd32f4xx_spi.o(i.spi_ti_mode_disable), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_ti_mode_enable), (10 bytes).
    Removing gd32f4xx_syscfg.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_syscfg.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_bootmode_config), (28 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_compensation_config), (24 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_deinit), (20 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_enet_phy_interface_config), (24 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_exmc_swap_config), (24 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_exti_line_config), (172 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_flag_get), (24 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_fmc_swap_config), (24 bytes).
    Removing gd32f4xx_timer.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_timer.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_timer.o(i.timer_auto_reload_shadow_disable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_auto_reload_shadow_enable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_automatic_output_disable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_automatic_output_enable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_autoreload_value_config), (4 bytes).
    Removing gd32f4xx_timer.o(i.timer_break_config), (30 bytes).
    Removing gd32f4xx_timer.o(i.timer_break_disable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_break_enable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_break_struct_para_init), (18 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_capture_value_register_read), (42 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_complementary_output_polarity_config), (70 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_complementary_output_state_config), (70 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_control_shadow_config), (24 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_control_shadow_update_config), (26 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_dma_request_source_select), (26 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_input_capture_prescaler_config), (90 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_input_struct_para_init), (16 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_clear_config), (90 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_config), (492 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_fast_config), (90 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_mode_config), (90 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_polarity_config), (92 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_pulse_value_config), (38 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_shadow_config), (90 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_state_config), (92 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_struct_para_init), (16 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_remap_config), (4 bytes).
    Removing gd32f4xx_timer.o(i.timer_counter_alignment), (16 bytes).
    Removing gd32f4xx_timer.o(i.timer_counter_down_direction), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_counter_read), (8 bytes).
    Removing gd32f4xx_timer.o(i.timer_counter_up_direction), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_counter_value_config), (4 bytes).
    Removing gd32f4xx_timer.o(i.timer_deinit), (388 bytes).
    Removing gd32f4xx_timer.o(i.timer_disable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_dma_disable), (8 bytes).
    Removing gd32f4xx_timer.o(i.timer_dma_enable), (8 bytes).
    Removing gd32f4xx_timer.o(i.timer_dma_transfer_config), (24 bytes).
    Removing gd32f4xx_timer.o(i.timer_enable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_event_software_generate), (8 bytes).
    Removing gd32f4xx_timer.o(i.timer_external_clock_mode0_config), (40 bytes).
    Removing gd32f4xx_timer.o(i.timer_external_clock_mode1_config), (32 bytes).
    Removing gd32f4xx_timer.o(i.timer_external_clock_mode1_disable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_external_trigger_as_external_clock_config), (166 bytes).
    Removing gd32f4xx_timer.o(i.timer_external_trigger_config), (30 bytes).
    Removing gd32f4xx_timer.o(i.timer_flag_clear), (6 bytes).
    Removing gd32f4xx_timer.o(i.timer_flag_get), (16 bytes).
    Removing gd32f4xx_timer.o(i.timer_hall_mode_config), (26 bytes).
    Removing gd32f4xx_timer.o(i.timer_init), (152 bytes).
    Removing gd32f4xx_timer.o(i.timer_input_capture_config), (326 bytes).
    Removing gd32f4xx_timer.o(i.timer_input_pwm_capture_config), (356 bytes).
    Removing gd32f4xx_timer.o(i.timer_input_trigger_source_select), (16 bytes).
    Removing gd32f4xx_timer.o(i.timer_internal_clock_config), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_internal_trigger_as_external_clock_config), (32 bytes).
    Removing gd32f4xx_timer.o(i.timer_interrupt_disable), (8 bytes).
    Removing gd32f4xx_timer.o(i.timer_interrupt_enable), (8 bytes).
    Removing gd32f4xx_timer.o(i.timer_interrupt_flag_clear), (6 bytes).
    Removing gd32f4xx_timer.o(i.timer_interrupt_flag_get), (24 bytes).
    Removing gd32f4xx_timer.o(i.timer_master_output_trigger_source_select), (16 bytes).
    Removing gd32f4xx_timer.o(i.timer_master_slave_mode_config), (26 bytes).
    Removing gd32f4xx_timer.o(i.timer_output_value_selection_config), (34 bytes).
    Removing gd32f4xx_timer.o(i.timer_prescaler_config), (14 bytes).
    Removing gd32f4xx_timer.o(i.timer_prescaler_read), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_primary_output_config), (24 bytes).
    Removing gd32f4xx_timer.o(i.timer_quadrature_decoder_mode_config), (64 bytes).
    Removing gd32f4xx_timer.o(i.timer_repetition_value_config), (4 bytes).
    Removing gd32f4xx_timer.o(i.timer_single_pulse_mode_config), (26 bytes).
    Removing gd32f4xx_timer.o(i.timer_slave_mode_select), (16 bytes).
    Removing gd32f4xx_timer.o(i.timer_struct_para_init), (22 bytes).
    Removing gd32f4xx_timer.o(i.timer_update_event_disable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_update_event_enable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_update_source_config), (26 bytes).
    Removing gd32f4xx_timer.o(i.timer_write_chxval_register_config), (34 bytes).
    Removing gd32f4xx_tli.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_tli.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_tli.o(i.tli_color_key_disable), (14 bytes).
    Removing gd32f4xx_tli.o(i.tli_color_key_enable), (14 bytes).
    Removing gd32f4xx_tli.o(i.tli_color_key_init), (16 bytes).
    Removing gd32f4xx_tli.o(i.tli_current_pos_get), (12 bytes).
    Removing gd32f4xx_tli.o(i.tli_deinit), (20 bytes).
    Removing gd32f4xx_tli.o(i.tli_disable), (20 bytes).
    Removing gd32f4xx_tli.o(i.tli_dither_config), (36 bytes).
    Removing gd32f4xx_tli.o(i.tli_enable), (20 bytes).
    Removing gd32f4xx_tli.o(i.tli_flag_get), (36 bytes).
    Removing gd32f4xx_tli.o(i.tli_init), (188 bytes).
    Removing gd32f4xx_tli.o(i.tli_interrupt_disable), (16 bytes).
    Removing gd32f4xx_tli.o(i.tli_interrupt_enable), (16 bytes).
    Removing gd32f4xx_tli.o(i.tli_interrupt_flag_clear), (16 bytes).
    Removing gd32f4xx_tli.o(i.tli_interrupt_flag_get), (36 bytes).
    Removing gd32f4xx_tli.o(i.tli_layer_disable), (14 bytes).
    Removing gd32f4xx_tli.o(i.tli_layer_enable), (14 bytes).
    Removing gd32f4xx_tli.o(i.tli_layer_init), (152 bytes).
    Removing gd32f4xx_tli.o(i.tli_layer_struct_para_init), (48 bytes).
    Removing gd32f4xx_tli.o(i.tli_layer_window_offset_modify), (228 bytes).
    Removing gd32f4xx_tli.o(i.tli_line_mark_set), (24 bytes).
    Removing gd32f4xx_tli.o(i.tli_lut_disable), (14 bytes).
    Removing gd32f4xx_tli.o(i.tli_lut_enable), (14 bytes).
    Removing gd32f4xx_tli.o(i.tli_lut_init), (28 bytes).
    Removing gd32f4xx_tli.o(i.tli_lut_struct_para_init), (12 bytes).
    Removing gd32f4xx_tli.o(i.tli_reload_config), (36 bytes).
    Removing gd32f4xx_tli.o(i.tli_struct_para_init), (34 bytes).
    Removing gd32f4xx_trng.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_trng.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_trng.o(i.trng_deinit), (20 bytes).
    Removing gd32f4xx_trng.o(i.trng_disable), (20 bytes).
    Removing gd32f4xx_trng.o(i.trng_enable), (20 bytes).
    Removing gd32f4xx_trng.o(i.trng_flag_get), (24 bytes).
    Removing gd32f4xx_trng.o(i.trng_get_true_random_data), (12 bytes).
    Removing gd32f4xx_trng.o(i.trng_interrupt_disable), (20 bytes).
    Removing gd32f4xx_trng.o(i.trng_interrupt_enable), (20 bytes).
    Removing gd32f4xx_trng.o(i.trng_interrupt_flag_clear), (16 bytes).
    Removing gd32f4xx_trng.o(i.trng_interrupt_flag_get), (24 bytes).
    Removing gd32f4xx_usart.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_usart.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_usart.o(i.usart_address_config), (20 bytes).
    Removing gd32f4xx_usart.o(i.usart_block_length_config), (28 bytes).
    Removing gd32f4xx_usart.o(i.usart_break_frame_coherence_config), (24 bytes).
    Removing gd32f4xx_usart.o(i.usart_data_first_config), (20 bytes).
    Removing gd32f4xx_usart.o(i.usart_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_dma_transmit_config), (20 bytes).
    Removing gd32f4xx_usart.o(i.usart_flag_clear), (52 bytes).
    Removing gd32f4xx_usart.o(i.usart_guard_time_config), (24 bytes).
    Removing gd32f4xx_usart.o(i.usart_halfduplex_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_halfduplex_enable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_hardware_flow_coherence_config), (24 bytes).
    Removing gd32f4xx_usart.o(i.usart_hardware_flow_cts_config), (20 bytes).
    Removing gd32f4xx_usart.o(i.usart_hardware_flow_rts_config), (20 bytes).
    Removing gd32f4xx_usart.o(i.usart_interrupt_flag_clear), (26 bytes).
    Removing gd32f4xx_usart.o(i.usart_invert_config), (104 bytes).
    Removing gd32f4xx_usart.o(i.usart_irda_lowpower_config), (20 bytes).
    Removing gd32f4xx_usart.o(i.usart_irda_mode_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_irda_mode_enable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_lin_break_detection_length_config), (20 bytes).
    Removing gd32f4xx_usart.o(i.usart_lin_mode_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_lin_mode_enable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_mute_mode_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_mute_mode_enable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_mute_mode_wakeup_config), (16 bytes).
    Removing gd32f4xx_usart.o(i.usart_oversample_config), (16 bytes).
    Removing gd32f4xx_usart.o(i.usart_parity_check_coherence_config), (24 bytes).
    Removing gd32f4xx_usart.o(i.usart_parity_config), (16 bytes).
    Removing gd32f4xx_usart.o(i.usart_prescaler_config), (16 bytes).
    Removing gd32f4xx_usart.o(i.usart_receiver_timeout_disable), (14 bytes).
    Removing gd32f4xx_usart.o(i.usart_receiver_timeout_enable), (14 bytes).
    Removing gd32f4xx_usart.o(i.usart_receiver_timeout_threshold_config), (20 bytes).
    Removing gd32f4xx_usart.o(i.usart_sample_bit_config), (16 bytes).
    Removing gd32f4xx_usart.o(i.usart_send_break), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_smartcard_autoretry_config), (26 bytes).
    Removing gd32f4xx_usart.o(i.usart_smartcard_mode_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_smartcard_mode_enable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_smartcard_mode_nack_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_smartcard_mode_nack_enable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_stop_bit_set), (16 bytes).
    Removing gd32f4xx_usart.o(i.usart_synchronous_clock_config), (34 bytes).
    Removing gd32f4xx_usart.o(i.usart_synchronous_clock_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_synchronous_clock_enable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_word_length_set), (16 bytes).
    Removing gd32f4xx_wwdgt.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_wwdgt.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_wwdgt.o(i.wwdgt_config), (28 bytes).
    Removing gd32f4xx_wwdgt.o(i.wwdgt_counter_update), (16 bytes).
    Removing gd32f4xx_wwdgt.o(i.wwdgt_deinit), (20 bytes).
    Removing gd32f4xx_wwdgt.o(i.wwdgt_enable), (20 bytes).
    Removing gd32f4xx_wwdgt.o(i.wwdgt_flag_clear), (12 bytes).
    Removing gd32f4xx_wwdgt.o(i.wwdgt_flag_get), (24 bytes).
    Removing gd32f4xx_wwdgt.o(i.wwdgt_interrupt_enable), (20 bytes).

908 unused section(s) (total 59982 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/dczerorl2.s                0x00000000   Number         0  __dczerorl2.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_ctype_table.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_exit.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/bigflt.c                         0x00000000   Number         0  bigflt0.o ABSOLUTE
    ../clib/btod.s                           0x00000000   Number         0  btod.o ABSOLUTE
    ../clib/ctype.c                          0x00000000   Number         0  isspace.o ABSOLUTE
    ../clib/fenv.c                           0x00000000   Number         0  _rserrno.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/locale.c                         0x00000000   Number         0  _wcrtomb.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_numeric_c.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_ctype_c.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  lludiv10.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr_w.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  strcmpv7m.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memcpy_v6.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memcpy_w.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  aeabi_memset.o ABSOLUTE
    ../clib/misc.s                           0x00000000   Number         0  printf_stubs.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_hex.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_intcommon.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_charcount.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  vsnprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __2sprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_str.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  noretval__2sprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_truncate.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_wchar.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_pad.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_int_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_int.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_longlong_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _sputc.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_wctomb.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_nopercent.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_infnan.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_common.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _snputc.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_p.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_o.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_i.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_d.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_u.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_e.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_g.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lli.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent_end.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lld.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llu.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_ll.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_l.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lc.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_ls.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llo.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llx.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_f.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_a.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_c.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_s.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_n.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_x.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  __0sscanf.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  scanf_hexfp.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _scanf.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _scanf_int.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _sgetc.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  scanf_char.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  scanf_fp.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _chval.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  scanf_infnan.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strncmp.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strlen.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  memcmp.o ABSOLUTE
    ../fplib/d2f.s                           0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/daddsub.s                       0x00000000   Number         0  daddsub_clz.o ABSOLUTE
    ../fplib/dcheck1.s                       0x00000000   Number         0  dcheck1.o ABSOLUTE
    ../fplib/dcmpi.s                         0x00000000   Number         0  dcmpi.o ABSOLUTE
    ../fplib/ddiv.s                          0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/deqf.s                          0x00000000   Number         0  deqf.o ABSOLUTE
    ../fplib/dleqf.s                         0x00000000   Number         0  dleqf.o ABSOLUTE
    ../fplib/dmul.s                          0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/dnaninf.s                       0x00000000   Number         0  dnaninf.o ABSOLUTE
    ../fplib/dretinf.s                       0x00000000   Number         0  dretinf.o ABSOLUTE
    ../fplib/drleqf.s                        0x00000000   Number         0  drleqf.o ABSOLUTE
    ../fplib/f2d.s                           0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/fnaninf.s                       0x00000000   Number         0  fnaninf.o ABSOLUTE
    ../fplib/fpconst.s                       0x00000000   Number         0  fpconst.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    ../fplib/fretinf.s                       0x00000000   Number         0  fretinf.o ABSOLUTE
    ../fplib/istatus.s                       0x00000000   Number         0  istatus.o ABSOLUTE
    ../fplib/printf1.s                       0x00000000   Number         0  printf1.o ABSOLUTE
    ../fplib/printf2.s                       0x00000000   Number         0  printf2.o ABSOLUTE
    ../fplib/printf2a.s                      0x00000000   Number         0  printf2a.o ABSOLUTE
    ../fplib/printf2b.s                      0x00000000   Number         0  printf2b.o ABSOLUTE
    ../fplib/retnan.s                        0x00000000   Number         0  retnan.o ABSOLUTE
    ../fplib/scalbn.s                        0x00000000   Number         0  scalbn.o ABSOLUTE
    ../fplib/scanf1.s                        0x00000000   Number         0  scanf1.o ABSOLUTE
    ../fplib/scanf2.s                        0x00000000   Number         0  scanf2.o ABSOLUTE
    ../fplib/scanf2a.s                       0x00000000   Number         0  scanf2a.o ABSOLUTE
    ../fplib/scanf2b.s                       0x00000000   Number         0  scanf2b.o ABSOLUTE
    ../fplib/trapv.s                         0x00000000   Number         0  trapv.o ABSOLUTE
    ../fplib/usenofp.s                       0x00000000   Number         0  usenofp.o ABSOLUTE
    ../mathlib/dunder.c                      0x00000000   Number         0  dunder.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ../mathlib/frexp.c                       0x00000000   Number         0  frexp.o ABSOLUTE
    ../mathlib/ldexp.c                       0x00000000   Number         0  ldexp_x.o ABSOLUTE
    ../mathlib/ldexp.c                       0x00000000   Number         0  ldexp.o ABSOLUTE
    ../mathlib/narrow.c                      0x00000000   Number         0  narrow.o ABSOLUTE
    ../mathlib/qnan.c                        0x00000000   Number         0  qnan.o ABSOLUTE
    ..\Compenents\Btn\Src\ebtn.c             0x00000000   Number         0  ebtn.o ABSOLUTE
    ..\Compenents\Fatfs\Src\diskio.c         0x00000000   Number         0  diskio.o ABSOLUTE
    ..\Compenents\Fatfs\Src\ff.c             0x00000000   Number         0  ff.o ABSOLUTE
    ..\Compenents\Fatfs\Src\unicode.c        0x00000000   Number         0  unicode.o ABSOLUTE
    ..\Compenents\Gd25qxx\Src\gd25qxx.c      0x00000000   Number         0  gd25qxx.o ABSOLUTE
    ..\Compenents\Gd30ad3344\Src\gd30ad3344.c 0x00000000   Number         0  gd30ad3344.o ABSOLUTE
    ..\Compenents\Oled\Src\oled.c            0x00000000   Number         0  oled.o ABSOLUTE
    ..\Compenents\Sdio\Src\sdio_sdcard.c     0x00000000   Number         0  sdio_sdcard.o ABSOLUTE
    ..\Core\Src\gd32f470vet6_bsp.c           0x00000000   Number         0  gd32f470vet6_bsp.o ABSOLUTE
    ..\Core\Src\gd32f4xx_it.c                0x00000000   Number         0  gd32f4xx_it.o ABSOLUTE
    ..\Core\Src\main.c                       0x00000000   Number         0  main.o ABSOLUTE
    ..\Core\Src\scheduler.c                  0x00000000   Number         0  scheduler.o ABSOLUTE
    ..\Core\Src\systick.c                    0x00000000   Number         0  systick.o ABSOLUTE
    ..\Firmware\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.s 0x00000000   Number         0  startup_gd32f450_470.o ABSOLUTE
    ..\Firmware\CMSIS\GD\GD32F4xx\Source\system_gd32f4xx.c 0x00000000   Number         0  system_gd32f4xx.o ABSOLUTE
    ..\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_adc.c 0x00000000   Number         0  gd32f4xx_adc.o ABSOLUTE
    ..\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_can.c 0x00000000   Number         0  gd32f4xx_can.o ABSOLUTE
    ..\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_crc.c 0x00000000   Number         0  gd32f4xx_crc.o ABSOLUTE
    ..\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_ctc.c 0x00000000   Number         0  gd32f4xx_ctc.o ABSOLUTE
    ..\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_dac.c 0x00000000   Number         0  gd32f4xx_dac.o ABSOLUTE
    ..\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_dbg.c 0x00000000   Number         0  gd32f4xx_dbg.o ABSOLUTE
    ..\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_dci.c 0x00000000   Number         0  gd32f4xx_dci.o ABSOLUTE
    ..\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_dma.c 0x00000000   Number         0  gd32f4xx_dma.o ABSOLUTE
    ..\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_enet.c 0x00000000   Number         0  gd32f4xx_enet.o ABSOLUTE
    ..\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_exmc.c 0x00000000   Number         0  gd32f4xx_exmc.o ABSOLUTE
    ..\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_exti.c 0x00000000   Number         0  gd32f4xx_exti.o ABSOLUTE
    ..\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_fmc.c 0x00000000   Number         0  gd32f4xx_fmc.o ABSOLUTE
    ..\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_fwdgt.c 0x00000000   Number         0  gd32f4xx_fwdgt.o ABSOLUTE
    ..\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_gpio.c 0x00000000   Number         0  gd32f4xx_gpio.o ABSOLUTE
    ..\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_i2c.c 0x00000000   Number         0  gd32f4xx_i2c.o ABSOLUTE
    ..\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_ipa.c 0x00000000   Number         0  gd32f4xx_ipa.o ABSOLUTE
    ..\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_iref.c 0x00000000   Number         0  gd32f4xx_iref.o ABSOLUTE
    ..\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_misc.c 0x00000000   Number         0  gd32f4xx_misc.o ABSOLUTE
    ..\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_pmu.c 0x00000000   Number         0  gd32f4xx_pmu.o ABSOLUTE
    ..\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_rcu.c 0x00000000   Number         0  gd32f4xx_rcu.o ABSOLUTE
    ..\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_rtc.c 0x00000000   Number         0  gd32f4xx_rtc.o ABSOLUTE
    ..\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_sdio.c 0x00000000   Number         0  gd32f4xx_sdio.o ABSOLUTE
    ..\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_spi.c 0x00000000   Number         0  gd32f4xx_spi.o ABSOLUTE
    ..\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_syscfg.c 0x00000000   Number         0  gd32f4xx_syscfg.o ABSOLUTE
    ..\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_timer.c 0x00000000   Number         0  gd32f4xx_timer.o ABSOLUTE
    ..\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_tli.c 0x00000000   Number         0  gd32f4xx_tli.o ABSOLUTE
    ..\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_trng.c 0x00000000   Number         0  gd32f4xx_trng.o ABSOLUTE
    ..\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_usart.c 0x00000000   Number         0  gd32f4xx_usart.o ABSOLUTE
    ..\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_wwdgt.c 0x00000000   Number         0  gd32f4xx_wwdgt.o ABSOLUTE
    ..\MyApps\Src\adc_app.c                  0x00000000   Number         0  adc_app.o ABSOLUTE
    ..\MyApps\Src\btn_app.c                  0x00000000   Number         0  btn_app.o ABSOLUTE
    ..\MyApps\Src\command_proc.c             0x00000000   Number         0  command_proc.o ABSOLUTE
    ..\MyApps\Src\flash_app.c                0x00000000   Number         0  flash_app.o ABSOLUTE
    ..\MyApps\Src\led_app.c                  0x00000000   Number         0  led_app.o ABSOLUTE
    ..\MyApps\Src\oled_app.c                 0x00000000   Number         0  oled_app.o ABSOLUTE
    ..\MyApps\Src\rtc_app.c                  0x00000000   Number         0  rtc_app.o ABSOLUTE
    ..\MyApps\Src\tf_app.c                   0x00000000   Number         0  tf_app.o ABSOLUTE
    ..\MyApps\Src\usart_app.c                0x00000000   Number         0  usart_app.o ABSOLUTE
    ..\\Compenents\\Fatfs\\Src\\diskio.c     0x00000000   Number         0  diskio.o ABSOLUTE
    ..\\Compenents\\Gd25qxx\\Src\\gd25qxx.c  0x00000000   Number         0  gd25qxx.o ABSOLUTE
    ..\\Compenents\\Gd30ad3344\\Src\\gd30ad3344.c 0x00000000   Number         0  gd30ad3344.o ABSOLUTE
    ..\\Compenents\\Oled\\Src\\oled.c        0x00000000   Number         0  oled.o ABSOLUTE
    ..\\Compenents\\Sdio\\Src\\sdio_sdcard.c 0x00000000   Number         0  sdio_sdcard.o ABSOLUTE
    ..\\Core\\Src\\gd32f470vet6_bsp.c        0x00000000   Number         0  gd32f470vet6_bsp.o ABSOLUTE
    ..\\Core\\Src\\gd32f4xx_it.c             0x00000000   Number         0  gd32f4xx_it.o ABSOLUTE
    ..\\Core\\Src\\main.c                    0x00000000   Number         0  main.o ABSOLUTE
    ..\\Core\\Src\\scheduler.c               0x00000000   Number         0  scheduler.o ABSOLUTE
    ..\\Core\\Src\\systick.c                 0x00000000   Number         0  systick.o ABSOLUTE
    ..\\Firmware\\CMSIS\\GD\\GD32F4xx\\Source\\system_gd32f4xx.c 0x00000000   Number         0  system_gd32f4xx.o ABSOLUTE
    ..\\Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_adc.c 0x00000000   Number         0  gd32f4xx_adc.o ABSOLUTE
    ..\\Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_can.c 0x00000000   Number         0  gd32f4xx_can.o ABSOLUTE
    ..\\Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_crc.c 0x00000000   Number         0  gd32f4xx_crc.o ABSOLUTE
    ..\\Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_ctc.c 0x00000000   Number         0  gd32f4xx_ctc.o ABSOLUTE
    ..\\Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_dac.c 0x00000000   Number         0  gd32f4xx_dac.o ABSOLUTE
    ..\\Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_dbg.c 0x00000000   Number         0  gd32f4xx_dbg.o ABSOLUTE
    ..\\Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_dci.c 0x00000000   Number         0  gd32f4xx_dci.o ABSOLUTE
    ..\\Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_dma.c 0x00000000   Number         0  gd32f4xx_dma.o ABSOLUTE
    ..\\Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_enet.c 0x00000000   Number         0  gd32f4xx_enet.o ABSOLUTE
    ..\\Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_exmc.c 0x00000000   Number         0  gd32f4xx_exmc.o ABSOLUTE
    ..\\Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_exti.c 0x00000000   Number         0  gd32f4xx_exti.o ABSOLUTE
    ..\\Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_fmc.c 0x00000000   Number         0  gd32f4xx_fmc.o ABSOLUTE
    ..\\Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_fwdgt.c 0x00000000   Number         0  gd32f4xx_fwdgt.o ABSOLUTE
    ..\\Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_gpio.c 0x00000000   Number         0  gd32f4xx_gpio.o ABSOLUTE
    ..\\Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_i2c.c 0x00000000   Number         0  gd32f4xx_i2c.o ABSOLUTE
    ..\\Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_ipa.c 0x00000000   Number         0  gd32f4xx_ipa.o ABSOLUTE
    ..\\Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_iref.c 0x00000000   Number         0  gd32f4xx_iref.o ABSOLUTE
    ..\\Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_misc.c 0x00000000   Number         0  gd32f4xx_misc.o ABSOLUTE
    ..\\Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_pmu.c 0x00000000   Number         0  gd32f4xx_pmu.o ABSOLUTE
    ..\\Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_rcu.c 0x00000000   Number         0  gd32f4xx_rcu.o ABSOLUTE
    ..\\Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_rtc.c 0x00000000   Number         0  gd32f4xx_rtc.o ABSOLUTE
    ..\\Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_sdio.c 0x00000000   Number         0  gd32f4xx_sdio.o ABSOLUTE
    ..\\Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_spi.c 0x00000000   Number         0  gd32f4xx_spi.o ABSOLUTE
    ..\\Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_syscfg.c 0x00000000   Number         0  gd32f4xx_syscfg.o ABSOLUTE
    ..\\Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_timer.c 0x00000000   Number         0  gd32f4xx_timer.o ABSOLUTE
    ..\\Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_tli.c 0x00000000   Number         0  gd32f4xx_tli.o ABSOLUTE
    ..\\Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_trng.c 0x00000000   Number         0  gd32f4xx_trng.o ABSOLUTE
    ..\\Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_usart.c 0x00000000   Number         0  gd32f4xx_usart.o ABSOLUTE
    ..\\Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_wwdgt.c 0x00000000   Number         0  gd32f4xx_wwdgt.o ABSOLUTE
    ..\\MyApps\\Src\\adc_app.c               0x00000000   Number         0  adc_app.o ABSOLUTE
    ..\\MyApps\\Src\\btn_app.c               0x00000000   Number         0  btn_app.o ABSOLUTE
    ..\\MyApps\\Src\\command_proc.c          0x00000000   Number         0  command_proc.o ABSOLUTE
    ..\\MyApps\\Src\\flash_app.c             0x00000000   Number         0  flash_app.o ABSOLUTE
    ..\\MyApps\\Src\\led_app.c               0x00000000   Number         0  led_app.o ABSOLUTE
    ..\\MyApps\\Src\\oled_app.c              0x00000000   Number         0  oled_app.o ABSOLUTE
    ..\\MyApps\\Src\\rtc_app.c               0x00000000   Number         0  rtc_app.o ABSOLUTE
    ..\\MyApps\\Src\\tf_app.c                0x00000000   Number         0  tf_app.o ABSOLUTE
    ..\\MyApps\\Src\\usart_app.c             0x00000000   Number         0  usart_app.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    RESET                                    0x08000000   Section      428  startup_gd32f450_470.o(RESET)
    !!!main                                  0x080001ac   Section        8  __main.o(!!!main)
    !!!scatter                               0x080001b4   Section       52  __scatter.o(!!!scatter)
    !!dczerorl2                              0x080001e8   Section       90  __dczerorl2.o(!!dczerorl2)
    !!handler_zi                             0x08000244   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$_printf_percent$$00000000  0x08000260   Section        0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    .ARM.Collect$$_printf_percent$$00000001  0x08000260   Section        6  _printf_n.o(.ARM.Collect$$_printf_percent$$00000001)
    .ARM.Collect$$_printf_percent$$00000002  0x08000266   Section        6  _printf_p.o(.ARM.Collect$$_printf_percent$$00000002)
    .ARM.Collect$$_printf_percent$$00000003  0x0800026c   Section        6  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    .ARM.Collect$$_printf_percent$$00000004  0x08000272   Section        6  _printf_e.o(.ARM.Collect$$_printf_percent$$00000004)
    .ARM.Collect$$_printf_percent$$00000005  0x08000278   Section        6  _printf_g.o(.ARM.Collect$$_printf_percent$$00000005)
    .ARM.Collect$$_printf_percent$$00000006  0x0800027e   Section        6  _printf_a.o(.ARM.Collect$$_printf_percent$$00000006)
    .ARM.Collect$$_printf_percent$$00000007  0x08000284   Section       10  _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007)
    .ARM.Collect$$_printf_percent$$00000008  0x0800028e   Section        6  _printf_i.o(.ARM.Collect$$_printf_percent$$00000008)
    .ARM.Collect$$_printf_percent$$00000009  0x08000294   Section        6  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    .ARM.Collect$$_printf_percent$$0000000A  0x0800029a   Section        6  _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A)
    .ARM.Collect$$_printf_percent$$0000000B  0x080002a0   Section        6  _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B)
    .ARM.Collect$$_printf_percent$$0000000C  0x080002a6   Section        6  _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C)
    .ARM.Collect$$_printf_percent$$0000000D  0x080002ac   Section        6  _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D)
    .ARM.Collect$$_printf_percent$$0000000E  0x080002b2   Section        6  _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E)
    .ARM.Collect$$_printf_percent$$0000000F  0x080002b8   Section        6  _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F)
    .ARM.Collect$$_printf_percent$$00000010  0x080002be   Section        6  _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010)
    .ARM.Collect$$_printf_percent$$00000011  0x080002c4   Section        6  _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011)
    .ARM.Collect$$_printf_percent$$00000012  0x080002ca   Section       10  _printf_l.o(.ARM.Collect$$_printf_percent$$00000012)
    .ARM.Collect$$_printf_percent$$00000013  0x080002d4   Section        6  _printf_c.o(.ARM.Collect$$_printf_percent$$00000013)
    .ARM.Collect$$_printf_percent$$00000014  0x080002da   Section        6  _printf_s.o(.ARM.Collect$$_printf_percent$$00000014)
    .ARM.Collect$$_printf_percent$$00000015  0x080002e0   Section        6  _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015)
    .ARM.Collect$$_printf_percent$$00000016  0x080002e6   Section        6  _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016)
    .ARM.Collect$$_printf_percent$$00000017  0x080002ec   Section        4  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    .ARM.Collect$$libinit$$00000000          0x080002f0   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000001          0x080002f2   Section        4  libinit2.o(.ARM.Collect$$libinit$$00000001)
    .ARM.Collect$$libinit$$00000004          0x080002f6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$0000000A          0x080002f6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    .ARM.Collect$$libinit$$0000000C          0x080002f6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x080002f6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$0000000F          0x080002f6   Section        6  libinit2.o(.ARM.Collect$$libinit$$0000000F)
    .ARM.Collect$$libinit$$00000011          0x080002fc   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000012          0x080002fc   Section       12  libinit2.o(.ARM.Collect$$libinit$$00000012)
    .ARM.Collect$$libinit$$00000013          0x08000308   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x08000308   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000016          0x08000308   Section       10  libinit2.o(.ARM.Collect$$libinit$$00000016)
    .ARM.Collect$$libinit$$00000017          0x08000312   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x08000312   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x08000312   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x08000312   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x08000312   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x08000312   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x08000312   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x08000312   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$0000002C          0x08000312   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    .ARM.Collect$$libinit$$0000002E          0x08000312   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x08000312   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x08000312   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000033          0x08000312   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000033)
    .ARM.Collect$$libshutdown$$00000000      0x08000314   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x08000316   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x08000316   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000006      0x08000316   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    .ARM.Collect$$libshutdown$$00000009      0x08000316   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    .ARM.Collect$$libshutdown$$0000000C      0x08000316   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000E      0x08000316   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    .ARM.Collect$$libshutdown$$00000011      0x08000316   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000011)
    .ARM.Collect$$libshutdown$$00000012      0x08000316   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000012)
    .ARM.Collect$$rtentry$$00000000          0x08000318   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x08000318   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x08000318   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x0800031e   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x0800031e   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x08000322   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x08000322   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x0800032a   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x0800032c   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x0800032c   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x08000330   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .text                                    0x08000338   Section       64  startup_gd32f450_470.o(.text)
    $v0                                      0x08000338   Number         0  startup_gd32f450_470.o(.text)
    .text                                    0x08000378   Section        0  vsnprintf.o(.text)
    .text                                    0x080003ac   Section        0  __2sprintf.o(.text)
    .text                                    0x080003d8   Section        0  __printf_flags_ss_wp.o(.text)
    .text                                    0x08000560   Section        0  __0sscanf.o(.text)
    .text                                    0x0800059c   Section        0  _scanf_int.o(.text)
    .text                                    0x080006e8   Section        0  memcmp.o(.text)
    .text                                    0x08000740   Section        0  strlen.o(.text)
    .text                                    0x0800077e   Section        0  strncmp.o(.text)
    .text                                    0x08000814   Section      138  rt_memcpy_v6.o(.text)
    .text                                    0x0800089e   Section      100  rt_memcpy_w.o(.text)
    .text                                    0x08000902   Section       68  rt_memclr.o(.text)
    .text                                    0x08000946   Section       78  rt_memclr_w.o(.text)
    .text                                    0x08000994   Section      128  strcmpv7m.o(.text)
    .text                                    0x08000a14   Section        0  heapauxi.o(.text)
    .text                                    0x08000a1a   Section        0  _printf_pad.o(.text)
    .text                                    0x08000a68   Section        0  _printf_truncate.o(.text)
    .text                                    0x08000a8c   Section        0  _printf_str.o(.text)
    .text                                    0x08000ae0   Section        0  _printf_dec.o(.text)
    .text                                    0x08000b58   Section        0  _printf_charcount.o(.text)
    .text                                    0x08000b80   Section        0  _printf_fp_dec.o(.text)
    _fp_digits                               0x08000b83   Thumb Code   432  _printf_fp_dec.o(.text)
    .text                                    0x08000fa0   Section        0  _printf_char_common.o(.text)
    _printf_input_char                       0x08000fa1   Thumb Code    10  _printf_char_common.o(.text)
    .text                                    0x08000fd0   Section        0  _sputc.o(.text)
    .text                                    0x08000fda   Section        0  _snputc.o(.text)
    .text                                    0x08000fec   Section        0  _printf_wctomb.o(.text)
    .text                                    0x080010a8   Section        0  _printf_longlong_dec.o(.text)
    .text                                    0x08001124   Section        0  _printf_oct_int_ll.o(.text)
    _printf_longlong_oct_internal            0x08001125   Thumb Code     0  _printf_oct_int_ll.o(.text)
    .text                                    0x08001194   Section        0  _printf_hex_int_ll_ptr.o(.text)
    _printf_hex_common                       0x08001195   Thumb Code     0  _printf_hex_int_ll_ptr.o(.text)
    .text                                    0x08001228   Section        0  _chval.o(.text)
    .text                                    0x08001244   Section        0  scanf_fp.o(.text)
    _fp_value                                0x08001245   Thumb Code   588  scanf_fp.o(.text)
    .text                                    0x0800173c   Section        0  scanf_char.o(.text)
    _scanf_char_input                        0x0800173d   Thumb Code    12  scanf_char.o(.text)
    .text                                    0x08001768   Section        0  _sgetc.o(.text)
    .text                                    0x080017a8   Section        8  rt_locale_intlibspace.o(.text)
    .text                                    0x080017b0   Section        8  rt_errno_addr_intlibspace.o(.text)
    .text                                    0x080017b8   Section      138  lludiv10.o(.text)
    .text                                    0x08001842   Section        0  isspace.o(.text)
    .text                                    0x08001854   Section        0  _printf_intcommon.o(.text)
    .text                                    0x08001908   Section        0  _printf_fp_hex.o(.text)
    .text                                    0x08001c04   Section        0  _printf_fp_infnan.o(.text)
    .text                                    0x08001c84   Section        0  _printf_char.o(.text)
    .text                                    0x08001cb0   Section        0  _printf_wchar.o(.text)
    .text                                    0x08001cdc   Section        0  _scanf.o(.text)
    .text                                    0x08002050   Section        0  bigflt0.o(.text)
    .text                                    0x08002134   Section        0  _wcrtomb.o(.text)
    .text                                    0x08002174   Section        8  libspace.o(.text)
    .text                                    0x0800217c   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x080021c8   Section       16  rt_ctype_table.o(.text)
    .text                                    0x080021d8   Section        0  _rserrno.o(.text)
    .text                                    0x080021f0   Section        0  scanf_hexfp.o(.text)
    .text                                    0x08002510   Section        0  scanf_infnan.o(.text)
    .text                                    0x08002644   Section        0  exit.o(.text)
    .text                                    0x08002656   Section       38  llshl.o(.text)
    .text                                    0x0800267c   Section        0  sys_exit.o(.text)
    .text                                    0x08002688   Section        2  use_no_semi.o(.text)
    .text                                    0x0800268a   Section        0  indicate_semi.o(.text)
    CL$$btod_d2e                             0x0800268a   Section       62  btod.o(CL$$btod_d2e)
    CL$$btod_d2e_denorm_low                  0x080026c8   Section       70  btod.o(CL$$btod_d2e_denorm_low)
    CL$$btod_d2e_norm_op1                    0x0800270e   Section       96  btod.o(CL$$btod_d2e_norm_op1)
    CL$$btod_div_common                      0x0800276e   Section      824  btod.o(CL$$btod_div_common)
    CL$$btod_e2d                             0x08002aa8   Section      132  btod.o(CL$$btod_e2d)
    CL$$btod_e2e                             0x08002b2c   Section      220  btod.o(CL$$btod_e2e)
    CL$$btod_ediv                            0x08002c08   Section       42  btod.o(CL$$btod_ediv)
    CL$$btod_edivd                           0x08002c32   Section       42  btod.o(CL$$btod_edivd)
    CL$$btod_emul                            0x08002c5c   Section       42  btod.o(CL$$btod_emul)
    CL$$btod_emuld                           0x08002c86   Section       42  btod.o(CL$$btod_emuld)
    CL$$btod_mult_common                     0x08002cb0   Section      580  btod.o(CL$$btod_mult_common)
    i.AD3344_Send_Data                       0x08002ef4   Section        0  gd30ad3344.o(i.AD3344_Send_Data)
    i.AdcDmaInit                             0x08002f04   Section        0  gd32f470vet6_bsp.o(i.AdcDmaInit)
    AdcDmaInit                               0x08002f05   Thumb Code    94  gd32f470vet6_bsp.o(i.AdcDmaInit)
    i.AdcPeriphInit                          0x08002f70   Section        0  gd32f470vet6_bsp.o(i.AdcPeriphInit)
    AdcPeriphInit                            0x08002f71   Thumb Code   158  gd32f470vet6_bsp.o(i.AdcPeriphInit)
    i.AdcTask                                0x08003018   Section        0  adc_app.o(i.AdcTask)
    i.BcdToDec                               0x0800306c   Section        0  gd32f470vet6_bsp.o(i.BcdToDec)
    i.BtnEventCallback                       0x08003080   Section        0  btn_app.o(i.BtnEventCallback)
    i.BtnGetState                            0x08003150   Section        0  gd32f470vet6_bsp.o(i.BtnGetState)
    BtnGetState                              0x08003151   Thumb Code   156  gd32f470vet6_bsp.o(i.BtnGetState)
    i.BtnPeriphInit                          0x080031f4   Section        0  gd32f470vet6_bsp.o(i.BtnPeriphInit)
    BtnPeriphInit                            0x080031f5   Thumb Code    96  gd32f470vet6_bsp.o(i.BtnPeriphInit)
    i.BtnTask                                0x0800326c   Section        0  btn_app.o(i.BtnTask)
    i.BusFault_Handler                       0x0800327c   Section        0  gd32f4xx_it.o(i.BusFault_Handler)
    i.CmdGetData                             0x08003280   Section        0  command_proc.o(i.CmdGetData)
    i.CmdProc                                0x08003308   Section        0  command_proc.o(i.CmdProc)
    i.CmdStartSample                         0x08003464   Section        0  command_proc.o(i.CmdStartSample)
    i.CmdStopSample                          0x08003478   Section        0  command_proc.o(i.CmdStopSample)
    i.Cmd_GetDeviceId                        0x08003498   Section        0  command_proc.o(i.Cmd_GetDeviceId)
    i.Cmd_GetRatio                           0x080034c0   Section        0  command_proc.o(i.Cmd_GetRatio)
    i.Cmd_GetRtc                             0x0800355c   Section        0  command_proc.o(i.Cmd_GetRtc)
    i.Cmd_SetLimit                           0x080035c4   Section        0  command_proc.o(i.Cmd_SetLimit)
    i.Cmd_SetRatio                           0x080036c4   Section        0  command_proc.o(i.Cmd_SetRatio)
    i.Cmd_SetRtc                             0x08003784   Section        0  command_proc.o(i.Cmd_SetRtc)
    i.DebugMon_Handler                       0x08003820   Section        0  gd32f4xx_it.o(i.DebugMon_Handler)
    i.DecToBcd                               0x08003824   Section        0  gd32f470vet6_bsp.o(i.DecToBcd)
    i.FlashPeriphInit                        0x08003840   Section        0  gd32f470vet6_bsp.o(i.FlashPeriphInit)
    FlashPeriphInit                          0x08003841   Thumb Code   144  gd32f470vet6_bsp.o(i.FlashPeriphInit)
    i.Flash_ReadDeviceId                     0x080038d8   Section        0  flash_app.o(i.Flash_ReadDeviceId)
    i.Gd30PeriphInit                         0x080038ec   Section        0  gd32f470vet6_bsp.o(i.Gd30PeriphInit)
    Gd30PeriphInit                           0x080038ed   Thumb Code   138  gd32f470vet6_bsp.o(i.Gd30PeriphInit)
    i.Gd30Task                               0x08003980   Section        0  adc_app.o(i.Gd30Task)
    i.GetDays                                0x08003a58   Section        0  gd32f470vet6_bsp.o(i.GetDays)
    GetDays                                  0x08003a59   Thumb Code    54  gd32f470vet6_bsp.o(i.GetDays)
    i.HardFault_Handler                      0x08003a94   Section        0  gd32f4xx_it.o(i.HardFault_Handler)
    i.I2C_Bus_Reset                          0x08003a98   Section        0  oled.o(i.I2C_Bus_Reset)
    I2C_Bus_Reset                            0x08003a99   Thumb Code   282  oled.o(i.I2C_Bus_Reset)
    i.IsLeapYear                             0x08003bc0   Section        0  gd32f470vet6_bsp.o(i.IsLeapYear)
    IsLeapYear                               0x08003bc1   Thumb Code    50  gd32f470vet6_bsp.o(i.IsLeapYear)
    i.LedDisp                                0x08003bf4   Section        0  gd32f470vet6_bsp.o(i.LedDisp)
    i.LedPeriphInit                          0x08003c7c   Section        0  gd32f470vet6_bsp.o(i.LedPeriphInit)
    LedPeriphInit                            0x08003c7d   Thumb Code    50  gd32f470vet6_bsp.o(i.LedPeriphInit)
    i.LedTask                                0x08003cb4   Section        0  led_app.o(i.LedTask)
    i.MemManage_Handler                      0x08003cc4   Section        0  gd32f4xx_it.o(i.MemManage_Handler)
    i.NMI_Handler                            0x08003cc8   Section        0  gd32f4xx_it.o(i.NMI_Handler)
    i.NVIC_SetPriority                       0x08003ccc   Section        0  systick.o(i.NVIC_SetPriority)
    NVIC_SetPriority                         0x08003ccd   Thumb Code    32  systick.o(i.NVIC_SetPriority)
    i.OLED_Clear                             0x08003cf4   Section        0  oled.o(i.OLED_Clear)
    i.OLED_Flush_buffer                      0x08003d34   Section        0  oled.o(i.OLED_Flush_buffer)
    i.OLED_Init                              0x08003d5c   Section        0  oled.o(i.OLED_Init)
    i.OLED_Set_Position                      0x08003d90   Section        0  oled.o(i.OLED_Set_Position)
    i.OLED_ShowChar                          0x08003db8   Section        0  oled.o(i.OLED_ShowChar)
    i.OLED_ShowStr                           0x08003e84   Section        0  oled.o(i.OLED_ShowStr)
    i.OLED_Write_cmd                         0x08003ec8   Section        0  oled.o(i.OLED_Write_cmd)
    i.OLED_Write_data                        0x08003ff0   Section        0  oled.o(i.OLED_Write_data)
    i.OLED_Write_data_batch                  0x08003ffc   Section        0  oled.o(i.OLED_Write_data_batch)
    i.OLED_Write_data_buffered               0x08004138   Section        0  oled.o(i.OLED_Write_data_buffered)
    i.OledDmaInit                            0x080041a4   Section        0  gd32f470vet6_bsp.o(i.OledDmaInit)
    OledDmaInit                              0x080041a5   Thumb Code   108  gd32f470vet6_bsp.o(i.OledDmaInit)
    i.OledDrawStr                            0x08004228   Section        0  gd32f470vet6_bsp.o(i.OledDrawStr)
    i.OledPeriphInit                         0x08004264   Section        0  gd32f470vet6_bsp.o(i.OledPeriphInit)
    OledPeriphInit                           0x08004265   Thumb Code   146  gd32f470vet6_bsp.o(i.OledPeriphInit)
    i.OledTask                               0x08004304   Section        0  oled_app.o(i.OledTask)
    i.PendSV_Handler                         0x08004344   Section        0  gd32f4xx_it.o(i.PendSV_Handler)
    i.ReadRtc                                0x08004348   Section        0  gd32f470vet6_bsp.o(i.ReadRtc)
    i.Rs485RxMode                            0x080043b4   Section        0  gd32f470vet6_bsp.o(i.Rs485RxMode)
    Rs485RxMode                              0x080043b5   Thumb Code    12  gd32f470vet6_bsp.o(i.Rs485RxMode)
    i.Rs485TxMode                            0x080043c4   Section        0  gd32f470vet6_bsp.o(i.Rs485TxMode)
    Rs485TxMode                              0x080043c5   Thumb Code    12  gd32f470vet6_bsp.o(i.Rs485TxMode)
    i.RtcPeriphInit                          0x080043d4   Section        0  gd32f470vet6_bsp.o(i.RtcPeriphInit)
    RtcPeriphInit                            0x080043d5   Thumb Code   138  gd32f470vet6_bsp.o(i.RtcPeriphInit)
    i.RtcPreConfig                           0x080044f8   Section        0  gd32f470vet6_bsp.o(i.RtcPreConfig)
    RtcPreConfig                             0x080044f9   Thumb Code    54  gd32f470vet6_bsp.o(i.RtcPreConfig)
    i.RtcTask                                0x08004538   Section        0  rtc_app.o(i.RtcTask)
    i.SDIO_IRQHandler                        0x08004548   Section        0  gd32f4xx_it.o(i.SDIO_IRQHandler)
    i.SVC_Handler                            0x08004550   Section        0  gd32f4xx_it.o(i.SVC_Handler)
    i.SampleProc                             0x08004554   Section        0  adc_app.o(i.SampleProc)
    i.SampleTask                             0x080045e4   Section        0  adc_app.o(i.SampleTask)
    i.SetRtc                                 0x08004620   Section        0  gd32f470vet6_bsp.o(i.SetRtc)
    i.SysInit                                0x080046ac   Section        0  gd32f470vet6_bsp.o(i.SysInit)
    i.SysTick_Handler                        0x080046dc   Section        0  gd32f4xx_it.o(i.SysTick_Handler)
    i.SystemInit                             0x080046f4   Section        0  system_gd32f4xx.o(i.SystemInit)
    i.TF_WriteConfig                         0x08004870   Section        0  tf_app.o(i.TF_WriteConfig)
    i.TF_WriteLimit                          0x08004978   Section        0  tf_app.o(i.TF_WriteLimit)
    i.TF_WriteRatio                          0x08004b10   Section        0  tf_app.o(i.TF_WriteRatio)
    i.TaskExeution                           0x08004bd8   Section        0  scheduler.o(i.TaskExeution)
    i.TfPeriphInit                           0x08004c34   Section        0  gd32f470vet6_bsp.o(i.TfPeriphInit)
    TfPeriphInit                             0x08004c35   Thumb Code    72  gd32f470vet6_bsp.o(i.TfPeriphInit)
    i.USART0_IRQHandler                      0x08004cbc   Section        0  gd32f4xx_it.o(i.USART0_IRQHandler)
    i.USART1_IRQHandler                      0x08004d60   Section        0  gd32f4xx_it.o(i.USART1_IRQHandler)
    i.UsageFault_Handler                     0x08004e04   Section        0  gd32f4xx_it.o(i.UsageFault_Handler)
    i.Usart0DmaInit                          0x08004e08   Section        0  gd32f470vet6_bsp.o(i.Usart0DmaInit)
    Usart0DmaInit                            0x08004e09   Thumb Code    96  gd32f470vet6_bsp.o(i.Usart0DmaInit)
    i.Usart0PeriphInit                       0x08004e74   Section        0  gd32f470vet6_bsp.o(i.Usart0PeriphInit)
    Usart0PeriphInit                         0x08004e75   Thumb Code   170  gd32f470vet6_bsp.o(i.Usart0PeriphInit)
    i.Usart0Printf                           0x08004f28   Section        0  gd32f470vet6_bsp.o(i.Usart0Printf)
    i.Usart0Task                             0x08004f98   Section        0  usart_app.o(i.Usart0Task)
    i.Usart1DmaInit                          0x08004fd0   Section        0  gd32f470vet6_bsp.o(i.Usart1DmaInit)
    Usart1DmaInit                            0x08004fd1   Thumb Code    96  gd32f470vet6_bsp.o(i.Usart1DmaInit)
    i.Usart1PeriphInit                       0x0800503c   Section        0  gd32f470vet6_bsp.o(i.Usart1PeriphInit)
    Usart1PeriphInit                         0x0800503d   Thumb Code   186  gd32f470vet6_bsp.o(i.Usart1PeriphInit)
    i.Usart1Printf                           0x08005100   Section        0  gd32f470vet6_bsp.o(i.Usart1Printf)
    i.Usart1Task                             0x08005184   Section        0  usart_app.o(i.Usart1Task)
    i.ValidateRtcTime                        0x080051b0   Section        0  gd32f470vet6_bsp.o(i.ValidateRtcTime)
    ValidateRtcTime                          0x080051b1   Thumb Code    96  gd32f470vet6_bsp.o(i.ValidateRtcTime)
    i.__ARM_fpclassify                       0x08005210   Section        0  fpclassify.o(i.__ARM_fpclassify)
    i.__hardfp___mathlib_tofloat             0x08005240   Section        0  narrow.o(i.__hardfp___mathlib_tofloat)
    i.__hardfp_ldexp                         0x08005338   Section        0  ldexp.o(i.__hardfp_ldexp)
    i.__mathlib_dbl_overflow                 0x08005408   Section        0  dunder.o(i.__mathlib_dbl_overflow)
    i.__mathlib_dbl_underflow                0x08005428   Section        0  dunder.o(i.__mathlib_dbl_underflow)
    i.__mathlib_narrow                       0x08005448   Section        0  narrow.o(i.__mathlib_narrow)
    i.__support_ldexp                        0x0800545a   Section        0  ldexp.o(i.__support_ldexp)
    i._is_digit                              0x0800546e   Section        0  __printf_wp.o(i._is_digit)
    i._soft_delay_                           0x0800547c   Section        0  system_gd32f4xx.o(i._soft_delay_)
    _soft_delay_                             0x0800547d   Thumb Code    28  system_gd32f4xx.o(i._soft_delay_)
    i.ad3344_init                            0x08005498   Section        0  gd30ad3344.o(i.ad3344_init)
    i.ad3344_read_data16                     0x080054e0   Section        0  gd30ad3344.o(i.ad3344_read_data16)
    i.ad3344_reset                           0x0800553c   Section        0  gd30ad3344.o(i.ad3344_reset)
    i.ad3344_spi_txrx16bit                   0x08005548   Section        0  gd30ad3344.o(i.ad3344_spi_txrx16bit)
    i.adc_calibration_enable                 0x080055b4   Section        0  gd32f4xx_adc.o(i.adc_calibration_enable)
    i.adc_channel_length_config              0x080055de   Section        0  gd32f4xx_adc.o(i.adc_channel_length_config)
    i.adc_clock_config                       0x08005630   Section        0  gd32f4xx_adc.o(i.adc_clock_config)
    i.adc_data_alignment_config              0x08005654   Section        0  gd32f4xx_adc.o(i.adc_data_alignment_config)
    i.adc_dma_mode_enable                    0x0800566a   Section        0  gd32f4xx_adc.o(i.adc_dma_mode_enable)
    i.adc_dma_request_after_last_enable      0x08005674   Section        0  gd32f4xx_adc.o(i.adc_dma_request_after_last_enable)
    i.adc_enable                             0x0800567e   Section        0  gd32f4xx_adc.o(i.adc_enable)
    i.adc_external_trigger_config            0x08005690   Section        0  gd32f4xx_adc.o(i.adc_external_trigger_config)
    i.adc_external_trigger_source_config     0x080056c4   Section        0  gd32f4xx_adc.o(i.adc_external_trigger_source_config)
    i.adc_routine_channel_config             0x080056f4   Section        0  gd32f4xx_adc.o(i.adc_routine_channel_config)
    i.adc_software_trigger_enable            0x080057a0   Section        0  gd32f4xx_adc.o(i.adc_software_trigger_enable)
    i.adc_special_function_config            0x080057c4   Section        0  gd32f4xx_adc.o(i.adc_special_function_config)
    i.adc_sync_mode_config                   0x08005820   Section        0  gd32f4xx_adc.o(i.adc_sync_mode_config)
    i.bit_array_and                          0x08005844   Section        0  ebtn.o(i.bit_array_and)
    bit_array_and                            0x08005845   Thumb Code    38  ebtn.o(i.bit_array_and)
    i.bit_array_assign                       0x0800586a   Section        0  ebtn.o(i.bit_array_assign)
    bit_array_assign                         0x0800586b   Thumb Code    46  ebtn.o(i.bit_array_assign)
    i.bit_array_cmp                          0x08005898   Section        0  ebtn.o(i.bit_array_cmp)
    bit_array_cmp                            0x08005899   Thumb Code    36  ebtn.o(i.bit_array_cmp)
    i.bit_array_get                          0x080058bc   Section        0  ebtn.o(i.bit_array_get)
    bit_array_get                            0x080058bd   Thumb Code    22  ebtn.o(i.bit_array_get)
    i.bit_array_num_bits_set                 0x080058d2   Section        0  ebtn.o(i.bit_array_num_bits_set)
    bit_array_num_bits_set                   0x080058d3   Thumb Code    84  ebtn.o(i.bit_array_num_bits_set)
    i.bit_array_or                           0x08005926   Section        0  ebtn.o(i.bit_array_or)
    bit_array_or                             0x08005927   Thumb Code    38  ebtn.o(i.bit_array_or)
    i.check_fs                               0x0800594c   Section        0  ff.o(i.check_fs)
    check_fs                                 0x0800594d   Thumb Code   138  ff.o(i.check_fs)
    i.chk_chr                                0x080059dc   Section        0  ff.o(i.chk_chr)
    chk_chr                                  0x080059dd   Thumb Code    20  ff.o(i.chk_chr)
    i.chk_mounted                            0x080059f0   Section        0  ff.o(i.chk_mounted)
    chk_mounted                              0x080059f1   Thumb Code   898  ff.o(i.chk_mounted)
    i.clust2sect                             0x08005d84   Section        0  ff.o(i.clust2sect)
    i.cmdsent_error_check                    0x08005da0   Section        0  sdio_sdcard.o(i.cmdsent_error_check)
    cmdsent_error_check                      0x08005da1   Thumb Code    40  sdio_sdcard.o(i.cmdsent_error_check)
    i.cmp_lfn                                0x08005dd0   Section        0  ff.o(i.cmp_lfn)
    cmp_lfn                                  0x08005dd1   Thumb Code   138  ff.o(i.cmp_lfn)
    i.create_chain                           0x08005e60   Section        0  ff.o(i.create_chain)
    create_chain                             0x08005e61   Thumb Code   202  ff.o(i.create_chain)
    i.create_name                            0x08005f2c   Section        0  ff.o(i.create_name)
    create_name                              0x08005f2d   Thumb Code   604  ff.o(i.create_name)
    i.delay_1ms                              0x0800619c   Section        0  systick.o(i.delay_1ms)
    i.delay_decrement                        0x080061b0   Section        0  systick.o(i.delay_decrement)
    i.delay_us                               0x080061c8   Section        0  gd30ad3344.o(i.delay_us)
    i.dir_find                               0x080061e2   Section        0  ff.o(i.dir_find)
    dir_find                                 0x080061e3   Thumb Code   222  ff.o(i.dir_find)
    i.dir_next                               0x080062c0   Section        0  ff.o(i.dir_next)
    dir_next                                 0x080062c1   Thumb Code   280  ff.o(i.dir_next)
    i.dir_register                           0x080063d8   Section        0  ff.o(i.dir_register)
    dir_register                             0x080063d9   Thumb Code   396  ff.o(i.dir_register)
    i.dir_sdi                                0x08006564   Section        0  ff.o(i.dir_sdi)
    dir_sdi                                  0x08006565   Thumb Code   156  ff.o(i.dir_sdi)
    i.disk_initialize                        0x08006600   Section        0  diskio.o(i.disk_initialize)
    i.disk_ioctl                             0x08006686   Section        0  diskio.o(i.disk_ioctl)
    i.disk_read                              0x0800668c   Section        0  diskio.o(i.disk_read)
    i.disk_status                            0x080066dc   Section        0  diskio.o(i.disk_status)
    i.disk_write                             0x080066e8   Section        0  diskio.o(i.disk_write)
    i.dma_channel_disable                    0x08006738   Section        0  gd32f4xx_dma.o(i.dma_channel_disable)
    i.dma_channel_enable                     0x08006758   Section        0  gd32f4xx_dma.o(i.dma_channel_enable)
    i.dma_channel_subperipheral_select       0x08006778   Section        0  gd32f4xx_dma.o(i.dma_channel_subperipheral_select)
    i.dma_circulation_disable                0x0800679e   Section        0  gd32f4xx_dma.o(i.dma_circulation_disable)
    i.dma_circulation_enable                 0x080067be   Section        0  gd32f4xx_dma.o(i.dma_circulation_enable)
    i.dma_deinit                             0x080067de   Section        0  gd32f4xx_dma.o(i.dma_deinit)
    i.dma_flag_clear                         0x08006884   Section        0  gd32f4xx_dma.o(i.dma_flag_clear)
    i.dma_flag_get                           0x080068c2   Section        0  gd32f4xx_dma.o(i.dma_flag_get)
    i.dma_flow_controller_config             0x0800690e   Section        0  gd32f4xx_dma.o(i.dma_flow_controller_config)
    i.dma_memory_address_config              0x0800694e   Section        0  gd32f4xx_dma.o(i.dma_memory_address_config)
    i.dma_multi_data_mode_init               0x08006970   Section        0  gd32f4xx_dma.o(i.dma_multi_data_mode_init)
    i.dma_receive_config                     0x08006ad4   Section        0  sdio_sdcard.o(i.dma_receive_config)
    dma_receive_config                       0x08006ad5   Thumb Code   170  sdio_sdcard.o(i.dma_receive_config)
    i.dma_single_data_mode_init              0x08006b88   Section        0  gd32f4xx_dma.o(i.dma_single_data_mode_init)
    i.dma_single_data_para_struct_init       0x08006ce0   Section        0  gd32f4xx_dma.o(i.dma_single_data_para_struct_init)
    i.dma_transfer_config                    0x08006d04   Section        0  sdio_sdcard.o(i.dma_transfer_config)
    dma_transfer_config                      0x08006d05   Thumb Code   172  sdio_sdcard.o(i.dma_transfer_config)
    i.dma_transfer_number_config             0x08006db8   Section        0  gd32f4xx_dma.o(i.dma_transfer_number_config)
    i.dma_transfer_number_get                0x08006dc8   Section        0  gd32f4xx_dma.o(i.dma_transfer_number_get)
    i.ebtn_combo_btn_add_btn                 0x08006dd8   Section        0  ebtn.o(i.ebtn_combo_btn_add_btn)
    i.ebtn_combo_btn_add_btn_by_idx          0x08006df8   Section        0  ebtn.o(i.ebtn_combo_btn_add_btn_by_idx)
    i.ebtn_get_btn_index_by_key_id           0x08006e18   Section        0  ebtn.o(i.ebtn_get_btn_index_by_key_id)
    i.ebtn_get_current_state                 0x08006e60   Section        0  ebtn.o(i.ebtn_get_current_state)
    ebtn_get_current_state                   0x08006e61   Thumb Code    82  ebtn.o(i.ebtn_get_current_state)
    i.ebtn_init                              0x08006eb8   Section        0  ebtn.o(i.ebtn_init)
    i.ebtn_process                           0x08006f14   Section        0  ebtn.o(i.ebtn_process)
    i.ebtn_process_btn                       0x08006f2e   Section        0  ebtn.o(i.ebtn_process_btn)
    ebtn_process_btn                         0x08006f2f   Thumb Code    62  ebtn.o(i.ebtn_process_btn)
    i.ebtn_process_btn_combo                 0x08006f6c   Section        0  ebtn.o(i.ebtn_process_btn_combo)
    ebtn_process_btn_combo                   0x08006f6d   Thumb Code   124  ebtn.o(i.ebtn_process_btn_combo)
    i.ebtn_process_with_curr_state           0x08006fe8   Section        0  ebtn.o(i.ebtn_process_with_curr_state)
    i.ebtn_set_combo_suppress_threshold      0x080071a8   Section        0  ebtn.o(i.ebtn_set_combo_suppress_threshold)
    i.ebtn_set_config                        0x080071b4   Section        0  ebtn.o(i.ebtn_set_config)
    i.ebtn_timer_sub                         0x080071c0   Section        0  ebtn.o(i.ebtn_timer_sub)
    ebtn_timer_sub                           0x080071c1   Thumb Code     6  ebtn.o(i.ebtn_timer_sub)
    i.f_close                                0x080071c6   Section        0  ff.o(i.f_close)
    i.f_mount                                0x080071dc   Section        0  ff.o(i.f_mount)
    i.f_open                                 0x08007208   Section        0  ff.o(i.f_open)
    i.f_read                                 0x0800737c   Section        0  ff.o(i.f_read)
    i.f_sync                                 0x0800754a   Section        0  ff.o(i.f_sync)
    i.f_write                                0x08007602   Section        0  ff.o(i.f_write)
    i.ff_convert                             0x08007810   Section        0  unicode.o(i.ff_convert)
    i.ff_wtoupper                            0x08007824   Section        0  unicode.o(i.ff_wtoupper)
    i.fit_lfn                                0x08007868   Section        0  ff.o(i.fit_lfn)
    fit_lfn                                  0x08007869   Thumb Code   122  ff.o(i.fit_lfn)
    i.follow_path                            0x080078e8   Section        0  ff.o(i.follow_path)
    follow_path                              0x080078e9   Thumb Code   158  ff.o(i.follow_path)
    i.frexp                                  0x08007988   Section        0  frexp.o(i.frexp)
    i.gen_numname                            0x08007a14   Section        0  ff.o(i.gen_numname)
    i.get_fat                                0x08007ad2   Section        0  ff.o(i.get_fat)
    i.get_fattime                            0x08007bb6   Section        0  diskio.o(i.get_fattime)
    i.gpio_af_set                            0x08007bba   Section        0  gd32f4xx_gpio.o(i.gpio_af_set)
    i.gpio_bit_reset                         0x08007c18   Section        0  gd32f4xx_gpio.o(i.gpio_bit_reset)
    i.gpio_bit_set                           0x08007c1c   Section        0  gd32f4xx_gpio.o(i.gpio_bit_set)
    i.gpio_bit_write                         0x08007c20   Section        0  gd32f4xx_gpio.o(i.gpio_bit_write)
    i.gpio_config                            0x08007c2c   Section        0  sdio_sdcard.o(i.gpio_config)
    gpio_config                              0x08007c2d   Thumb Code   106  sdio_sdcard.o(i.gpio_config)
    i.gpio_input_bit_get                     0x08007ca0   Section        0  gd32f4xx_gpio.o(i.gpio_input_bit_get)
    i.gpio_mode_set                          0x08007cb0   Section        0  gd32f4xx_gpio.o(i.gpio_mode_set)
    i.gpio_output_options_set                0x08007cfe   Section        0  gd32f4xx_gpio.o(i.gpio_output_options_set)
    i.i2c_ack_config                         0x08007d40   Section        0  gd32f4xx_i2c.o(i.i2c_ack_config)
    i.i2c_clock_config                       0x08007d50   Section        0  gd32f4xx_i2c.o(i.i2c_clock_config)
    i.i2c_deinit                             0x08007e34   Section        0  gd32f4xx_i2c.o(i.i2c_deinit)
    i.i2c_dma_config                         0x08007e8c   Section        0  gd32f4xx_i2c.o(i.i2c_dma_config)
    i.i2c_enable                             0x08007e9c   Section        0  gd32f4xx_i2c.o(i.i2c_enable)
    i.i2c_flag_clear                         0x08007ea6   Section        0  gd32f4xx_i2c.o(i.i2c_flag_clear)
    i.i2c_flag_get                           0x08007ece   Section        0  gd32f4xx_i2c.o(i.i2c_flag_get)
    i.i2c_master_addressing                  0x08007eec   Section        0  gd32f4xx_i2c.o(i.i2c_master_addressing)
    i.i2c_mode_addr_config                   0x08007f00   Section        0  gd32f4xx_i2c.o(i.i2c_mode_addr_config)
    i.i2c_start_on_bus                       0x08007f1c   Section        0  gd32f4xx_i2c.o(i.i2c_start_on_bus)
    i.i2c_stop_on_bus                        0x08007f26   Section        0  gd32f4xx_i2c.o(i.i2c_stop_on_bus)
    i.main                                   0x08007f30   Section        0  main.o(i.main)
    i.mem_cmp                                0x08007f40   Section        0  ff.o(i.mem_cmp)
    mem_cmp                                  0x08007f41   Thumb Code    38  ff.o(i.mem_cmp)
    i.mem_cpy                                0x08007f66   Section        0  ff.o(i.mem_cpy)
    mem_cpy                                  0x08007f67   Thumb Code    26  ff.o(i.mem_cpy)
    i.mem_set                                0x08007f80   Section        0  ff.o(i.mem_set)
    mem_set                                  0x08007f81   Thumb Code    20  ff.o(i.mem_set)
    i.move_window                            0x08007f94   Section        0  ff.o(i.move_window)
    move_window                              0x08007f95   Thumb Code   114  ff.o(i.move_window)
    i.nvic_irq_enable                        0x08008008   Section        0  gd32f4xx_misc.o(i.nvic_irq_enable)
    i.nvic_priority_group_set                0x080080cc   Section        0  gd32f4xx_misc.o(i.nvic_priority_group_set)
    i.pmu_backup_write_enable                0x080080e0   Section        0  gd32f4xx_pmu.o(i.pmu_backup_write_enable)
    i.prv_get_combo_btn_by_key_id            0x080080f4   Section        0  ebtn.o(i.prv_get_combo_btn_by_key_id)
    prv_get_combo_btn_by_key_id              0x080080f5   Thumb Code    70  ebtn.o(i.prv_get_combo_btn_by_key_id)
    i.prv_process_btn                        0x08008140   Section        0  ebtn.o(i.prv_process_btn)
    prv_process_btn                          0x08008141   Thumb Code   878  ebtn.o(i.prv_process_btn)
    i.put_fat                                0x080084b4   Section        0  ff.o(i.put_fat)
    i.r1_error_check                         0x080085ec   Section        0  sdio_sdcard.o(i.r1_error_check)
    r1_error_check                           0x080085ed   Thumb Code   120  sdio_sdcard.o(i.r1_error_check)
    i.r1_error_type_check                    0x08008670   Section        0  sdio_sdcard.o(i.r1_error_type_check)
    r1_error_type_check                      0x08008671   Thumb Code   174  sdio_sdcard.o(i.r1_error_type_check)
    i.r2_error_check                         0x08008720   Section        0  sdio_sdcard.o(i.r2_error_check)
    r2_error_check                           0x08008721   Thumb Code    70  sdio_sdcard.o(i.r2_error_check)
    i.r3_error_check                         0x08008770   Section        0  sdio_sdcard.o(i.r3_error_check)
    r3_error_check                           0x08008771   Thumb Code    52  sdio_sdcard.o(i.r3_error_check)
    i.r6_error_check                         0x080087ac   Section        0  sdio_sdcard.o(i.r6_error_check)
    r6_error_check                           0x080087ad   Thumb Code   158  sdio_sdcard.o(i.r6_error_check)
    i.r7_error_check                         0x08008854   Section        0  sdio_sdcard.o(i.r7_error_check)
    r7_error_check                           0x08008855   Thumb Code    74  sdio_sdcard.o(i.r7_error_check)
    i.rcu_all_reset_flag_clear               0x080088a4   Section        0  gd32f4xx_rcu.o(i.rcu_all_reset_flag_clear)
    i.rcu_clock_freq_get                     0x080088b8   Section        0  gd32f4xx_rcu.o(i.rcu_clock_freq_get)
    i.rcu_config                             0x080089dc   Section        0  sdio_sdcard.o(i.rcu_config)
    rcu_config                               0x080089dd   Thumb Code    36  sdio_sdcard.o(i.rcu_config)
    i.rcu_flag_get                           0x08008a00   Section        0  gd32f4xx_rcu.o(i.rcu_flag_get)
    i.rcu_osci_on                            0x08008a24   Section        0  gd32f4xx_rcu.o(i.rcu_osci_on)
    i.rcu_osci_stab_wait                     0x08008a48   Section        0  gd32f4xx_rcu.o(i.rcu_osci_stab_wait)
    i.rcu_periph_clock_enable                0x08008ba4   Section        0  gd32f4xx_rcu.o(i.rcu_periph_clock_enable)
    i.rcu_periph_reset_disable               0x08008bc8   Section        0  gd32f4xx_rcu.o(i.rcu_periph_reset_disable)
    i.rcu_periph_reset_enable                0x08008bec   Section        0  gd32f4xx_rcu.o(i.rcu_periph_reset_enable)
    i.rcu_rtc_clock_config                   0x08008c10   Section        0  gd32f4xx_rcu.o(i.rcu_rtc_clock_config)
    i.remove_chain                           0x08008c28   Section        0  ff.o(i.remove_chain)
    remove_chain                             0x08008c29   Thumb Code   104  ff.o(i.remove_chain)
    i.rtc_current_time_get                   0x08008c90   Section        0  gd32f4xx_rtc.o(i.rtc_current_time_get)
    i.rtc_init                               0x08008cf4   Section        0  gd32f4xx_rtc.o(i.rtc_init)
    i.rtc_init_mode_enter                    0x08008db8   Section        0  gd32f4xx_rtc.o(i.rtc_init_mode_enter)
    i.rtc_init_mode_exit                     0x08008e00   Section        0  gd32f4xx_rtc.o(i.rtc_init_mode_exit)
    i.rtc_register_sync_wait                 0x08008e14   Section        0  gd32f4xx_rtc.o(i.rtc_register_sync_wait)
    i.sd_block_read                          0x08008e74   Section        0  sdio_sdcard.o(i.sd_block_read)
    i.sd_block_write                         0x0800908c   Section        0  sdio_sdcard.o(i.sd_block_write)
    i.sd_bus_mode_config                     0x080093ac   Section        0  sdio_sdcard.o(i.sd_bus_mode_config)
    i.sd_bus_width_config                    0x08009440   Section        0  sdio_sdcard.o(i.sd_bus_width_config)
    sd_bus_width_config                      0x08009441   Thumb Code   242  sdio_sdcard.o(i.sd_bus_width_config)
    i.sd_card_information_get                0x0800953c   Section        0  sdio_sdcard.o(i.sd_card_information_get)
    i.sd_card_init                           0x080097fc   Section        0  sdio_sdcard.o(i.sd_card_init)
    i.sd_card_select_deselect                0x08009918   Section        0  sdio_sdcard.o(i.sd_card_select_deselect)
    i.sd_card_state_get                      0x08009940   Section        0  sdio_sdcard.o(i.sd_card_state_get)
    sd_card_state_get                        0x08009941   Thumb Code   166  sdio_sdcard.o(i.sd_card_state_get)
    i.sd_cardstatus_get                      0x080099f8   Section        0  sdio_sdcard.o(i.sd_cardstatus_get)
    i.sd_datablocksize_get                   0x08009a40   Section        0  sdio_sdcard.o(i.sd_datablocksize_get)
    sd_datablocksize_get                     0x08009a41   Thumb Code    24  sdio_sdcard.o(i.sd_datablocksize_get)
    i.sd_init                                0x08009a58   Section        0  sdio_sdcard.o(i.sd_init)
    i.sd_interrupts_process                  0x08009aa0   Section        0  sdio_sdcard.o(i.sd_interrupts_process)
    i.sd_multiblocks_read                    0x08009bd0   Section        0  sdio_sdcard.o(i.sd_multiblocks_read)
    i.sd_multiblocks_write                   0x08009e6c   Section        0  sdio_sdcard.o(i.sd_multiblocks_write)
    i.sd_power_on                            0x0800a204   Section        0  sdio_sdcard.o(i.sd_power_on)
    i.sd_scr_get                             0x0800a330   Section        0  sdio_sdcard.o(i.sd_scr_get)
    sd_scr_get                               0x0800a331   Thumb Code   344  sdio_sdcard.o(i.sd_scr_get)
    i.sd_transfer_mode_config                0x0800a48c   Section        0  sdio_sdcard.o(i.sd_transfer_mode_config)
    i.sd_transfer_stop                       0x0800a4a4   Section        0  sdio_sdcard.o(i.sd_transfer_stop)
    i.sdio_bus_mode_set                      0x0800a4c8   Section        0  gd32f4xx_sdio.o(i.sdio_bus_mode_set)
    i.sdio_clock_config                      0x0800a4e4   Section        0  gd32f4xx_sdio.o(i.sdio_clock_config)
    i.sdio_clock_enable                      0x0800a518   Section        0  gd32f4xx_sdio.o(i.sdio_clock_enable)
    i.sdio_command_index_get                 0x0800a52c   Section        0  gd32f4xx_sdio.o(i.sdio_command_index_get)
    i.sdio_command_response_config           0x0800a538   Section        0  gd32f4xx_sdio.o(i.sdio_command_response_config)
    i.sdio_csm_enable                        0x0800a570   Section        0  gd32f4xx_sdio.o(i.sdio_csm_enable)
    i.sdio_data_config                       0x0800a584   Section        0  gd32f4xx_sdio.o(i.sdio_data_config)
    i.sdio_data_read                         0x0800a5c0   Section        0  gd32f4xx_sdio.o(i.sdio_data_read)
    i.sdio_data_transfer_config              0x0800a5cc   Section        0  gd32f4xx_sdio.o(i.sdio_data_transfer_config)
    i.sdio_data_write                        0x0800a5e8   Section        0  gd32f4xx_sdio.o(i.sdio_data_write)
    i.sdio_deinit                            0x0800a5f4   Section        0  gd32f4xx_sdio.o(i.sdio_deinit)
    i.sdio_dma_disable                       0x0800a608   Section        0  gd32f4xx_sdio.o(i.sdio_dma_disable)
    i.sdio_dma_enable                        0x0800a61c   Section        0  gd32f4xx_sdio.o(i.sdio_dma_enable)
    i.sdio_dsm_disable                       0x0800a630   Section        0  gd32f4xx_sdio.o(i.sdio_dsm_disable)
    i.sdio_dsm_enable                        0x0800a644   Section        0  gd32f4xx_sdio.o(i.sdio_dsm_enable)
    i.sdio_flag_clear                        0x0800a658   Section        0  gd32f4xx_sdio.o(i.sdio_flag_clear)
    i.sdio_flag_get                          0x0800a664   Section        0  gd32f4xx_sdio.o(i.sdio_flag_get)
    i.sdio_hardware_clock_disable            0x0800a678   Section        0  gd32f4xx_sdio.o(i.sdio_hardware_clock_disable)
    i.sdio_interrupt_disable                 0x0800a68c   Section        0  gd32f4xx_sdio.o(i.sdio_interrupt_disable)
    i.sdio_interrupt_enable                  0x0800a69c   Section        0  gd32f4xx_sdio.o(i.sdio_interrupt_enable)
    i.sdio_interrupt_flag_clear              0x0800a6ac   Section        0  gd32f4xx_sdio.o(i.sdio_interrupt_flag_clear)
    i.sdio_interrupt_flag_get                0x0800a6b8   Section        0  gd32f4xx_sdio.o(i.sdio_interrupt_flag_get)
    i.sdio_power_state_get                   0x0800a6cc   Section        0  gd32f4xx_sdio.o(i.sdio_power_state_get)
    i.sdio_power_state_set                   0x0800a6d8   Section        0  gd32f4xx_sdio.o(i.sdio_power_state_set)
    i.sdio_response_get                      0x0800a6e4   Section        0  gd32f4xx_sdio.o(i.sdio_response_get)
    i.sdio_wait_type_set                     0x0800a720   Section        0  gd32f4xx_sdio.o(i.sdio_wait_type_set)
    i.spi_dma_disable                        0x0800a73c   Section        0  gd32f4xx_spi.o(i.spi_dma_disable)
    i.spi_dma_enable                         0x0800a752   Section        0  gd32f4xx_spi.o(i.spi_dma_enable)
    i.spi_enable                             0x0800a768   Section        0  gd32f4xx_spi.o(i.spi_enable)
    i.spi_flash_buffer_read                  0x0800a774   Section        0  gd25qxx.o(i.spi_flash_buffer_read)
    i.spi_flash_init                         0x0800a7c8   Section        0  gd25qxx.o(i.spi_flash_init)
    i.spi_flash_send_byte_dma                0x0800a7e4   Section        0  gd25qxx.o(i.spi_flash_send_byte_dma)
    i.spi_i2s_data_receive                   0x0800a8e0   Section        0  gd32f4xx_spi.o(i.spi_i2s_data_receive)
    i.spi_i2s_data_transmit                  0x0800a8e8   Section        0  gd32f4xx_spi.o(i.spi_i2s_data_transmit)
    i.spi_i2s_flag_get                       0x0800a8ec   Section        0  gd32f4xx_spi.o(i.spi_i2s_flag_get)
    i.spi_init                               0x0800a8fc   Section        0  gd32f4xx_spi.o(i.spi_init)
    i.sum_sfn                                0x0800a92e   Section        0  ff.o(i.sum_sfn)
    sum_sfn                                  0x0800a92f   Thumb Code    32  ff.o(i.sum_sfn)
    i.sync                                   0x0800a94e   Section        0  ff.o(i.sync)
    sync                                     0x0800a94f   Thumb Code   202  ff.o(i.sync)
    i.system_clock_240m_25m_hxtal            0x0800aa18   Section        0  system_gd32f4xx.o(i.system_clock_240m_25m_hxtal)
    system_clock_240m_25m_hxtal              0x0800aa19   Thumb Code   258  system_gd32f4xx.o(i.system_clock_240m_25m_hxtal)
    i.system_clock_config                    0x0800ab28   Section        0  system_gd32f4xx.o(i.system_clock_config)
    system_clock_config                      0x0800ab29   Thumb Code     8  system_gd32f4xx.o(i.system_clock_config)
    i.systick_config                         0x0800ab30   Section        0  systick.o(i.systick_config)
    i.usart_baudrate_set                     0x0800ab80   Section        0  gd32f4xx_usart.o(i.usart_baudrate_set)
    i.usart_data_receive                     0x0800ac68   Section        0  gd32f4xx_usart.o(i.usart_data_receive)
    i.usart_data_transmit                    0x0800ac72   Section        0  gd32f4xx_usart.o(i.usart_data_transmit)
    i.usart_deinit                           0x0800ac7c   Section        0  gd32f4xx_usart.o(i.usart_deinit)
    i.usart_dma_receive_config               0x0800ad58   Section        0  gd32f4xx_usart.o(i.usart_dma_receive_config)
    i.usart_enable                           0x0800ad6c   Section        0  gd32f4xx_usart.o(i.usart_enable)
    i.usart_flag_get                         0x0800ad76   Section        0  gd32f4xx_usart.o(i.usart_flag_get)
    i.usart_interrupt_disable                0x0800ad94   Section        0  gd32f4xx_usart.o(i.usart_interrupt_disable)
    i.usart_interrupt_enable                 0x0800adae   Section        0  gd32f4xx_usart.o(i.usart_interrupt_enable)
    i.usart_interrupt_flag_get               0x0800adc8   Section        0  gd32f4xx_usart.o(i.usart_interrupt_flag_get)
    i.usart_receive_config                   0x0800ae00   Section        0  gd32f4xx_usart.o(i.usart_receive_config)
    i.usart_transmit_config                  0x0800ae10   Section        0  gd32f4xx_usart.o(i.usart_transmit_config)
    i.validate                               0x0800ae20   Section        0  ff.o(i.validate)
    validate                                 0x0800ae21   Thumb Code    42  ff.o(i.validate)
    locale$$code                             0x0800ae4c   Section       44  lc_numeric_c.o(locale$$code)
    locale$$code                             0x0800ae78   Section       44  lc_ctype_c.o(locale$$code)
    x$fpl$d2f                                0x0800aea4   Section       98  d2f.o(x$fpl$d2f)
    $v0                                      0x0800aea4   Number         0  d2f.o(x$fpl$d2f)
    x$fpl$dcheck1                            0x0800af08   Section       16  dcheck1.o(x$fpl$dcheck1)
    $v0                                      0x0800af08   Number         0  dcheck1.o(x$fpl$dcheck1)
    x$fpl$dcmpinf                            0x0800af18   Section       24  dcmpi.o(x$fpl$dcmpinf)
    $v0                                      0x0800af18   Number         0  dcmpi.o(x$fpl$dcmpinf)
    x$fpl$deqf                               0x0800af30   Section      120  deqf.o(x$fpl$deqf)
    $v0                                      0x0800af30   Number         0  deqf.o(x$fpl$deqf)
    x$fpl$dleqf                              0x0800afa8   Section      120  dleqf.o(x$fpl$dleqf)
    $v0                                      0x0800afa8   Number         0  dleqf.o(x$fpl$dleqf)
    x$fpl$dmul                               0x0800b020   Section      340  dmul.o(x$fpl$dmul)
    $v0                                      0x0800b020   Number         0  dmul.o(x$fpl$dmul)
    x$fpl$dnaninf                            0x0800b174   Section      156  dnaninf.o(x$fpl$dnaninf)
    $v0                                      0x0800b174   Number         0  dnaninf.o(x$fpl$dnaninf)
    x$fpl$dretinf                            0x0800b210   Section       12  dretinf.o(x$fpl$dretinf)
    $v0                                      0x0800b210   Number         0  dretinf.o(x$fpl$dretinf)
    x$fpl$drleqf                             0x0800b21c   Section      108  drleqf.o(x$fpl$drleqf)
    $v0                                      0x0800b21c   Number         0  drleqf.o(x$fpl$drleqf)
    x$fpl$f2d                                0x0800b288   Section       86  f2d.o(x$fpl$f2d)
    $v0                                      0x0800b288   Number         0  f2d.o(x$fpl$f2d)
    x$fpl$fnaninf                            0x0800b2de   Section      140  fnaninf.o(x$fpl$fnaninf)
    $v0                                      0x0800b2de   Number         0  fnaninf.o(x$fpl$fnaninf)
    x$fpl$fpinit                             0x0800b36a   Section       10  fpinit.o(x$fpl$fpinit)
    $v0                                      0x0800b36a   Number         0  fpinit.o(x$fpl$fpinit)
    x$fpl$fretinf                            0x0800b374   Section       10  fretinf.o(x$fpl$fretinf)
    $v0                                      0x0800b374   Number         0  fretinf.o(x$fpl$fretinf)
    x$fpl$ieeestatus                         0x0800b37e   Section        6  istatus.o(x$fpl$ieeestatus)
    $v0                                      0x0800b37e   Number         0  istatus.o(x$fpl$ieeestatus)
    x$fpl$printf1                            0x0800b384   Section        4  printf1.o(x$fpl$printf1)
    $v0                                      0x0800b384   Number         0  printf1.o(x$fpl$printf1)
    x$fpl$printf2                            0x0800b388   Section        4  printf2.o(x$fpl$printf2)
    $v0                                      0x0800b388   Number         0  printf2.o(x$fpl$printf2)
    x$fpl$retnan                             0x0800b38c   Section      100  retnan.o(x$fpl$retnan)
    $v0                                      0x0800b38c   Number         0  retnan.o(x$fpl$retnan)
    x$fpl$scalbn                             0x0800b3f0   Section       92  scalbn.o(x$fpl$scalbn)
    $v0                                      0x0800b3f0   Number         0  scalbn.o(x$fpl$scalbn)
    x$fpl$scanf1                             0x0800b44c   Section        4  scanf1.o(x$fpl$scanf1)
    $v0                                      0x0800b44c   Number         0  scanf1.o(x$fpl$scanf1)
    x$fpl$scanf2                             0x0800b450   Section        8  scanf2.o(x$fpl$scanf2)
    $v0                                      0x0800b450   Number         0  scanf2.o(x$fpl$scanf2)
    x$fpl$trapveneer                         0x0800b458   Section       48  trapv.o(x$fpl$trapveneer)
    $v0                                      0x0800b458   Number         0  trapv.o(x$fpl$trapveneer)
    .constdata                               0x0800b488   Section       64  gd32f470vet6_bsp.o(.constdata)
    x$fpl$usenofp                            0x0800b488   Section        0  usenofp.o(x$fpl$usenofp)
    defaul_ebtn_param                        0x0800b488   Data          14  gd32f470vet6_bsp.o(.constdata)
    days_in_month                            0x0800b498   Data          48  gd32f470vet6_bsp.o(.constdata)
    .constdata                               0x0800b4c8   Section       24  tf_app.o(.constdata)
    .constdata                               0x0800b4e0   Section     2712  oled.o(.constdata)
    F8X16                                    0x0800b708   Data        1520  oled.o(.constdata)
    .constdata                               0x0800bf78   Section       13  ff.o(.constdata)
    LfnOfs                                   0x0800bf78   Data          13  ff.o(.constdata)
    .constdata                               0x0800bf86   Section      376  unicode.o(.constdata)
    tbl_lower                                0x0800bf86   Data         188  unicode.o(.constdata)
    tbl_upper                                0x0800c042   Data         188  unicode.o(.constdata)
    .constdata                               0x0800c0fe   Section       17  __printf_flags_ss_wp.o(.constdata)
    maptable                                 0x0800c0fe   Data          17  __printf_flags_ss_wp.o(.constdata)
    .constdata                               0x0800c110   Section        8  _printf_wctomb.o(.constdata)
    initial_mbstate                          0x0800c110   Data           8  _printf_wctomb.o(.constdata)
    .constdata                               0x0800c118   Section       40  _printf_hex_int_ll_ptr.o(.constdata)
    uc_hextab                                0x0800c118   Data          20  _printf_hex_int_ll_ptr.o(.constdata)
    lc_hextab                                0x0800c12c   Data          20  _printf_hex_int_ll_ptr.o(.constdata)
    .constdata                               0x0800c140   Section       38  _printf_fp_hex.o(.constdata)
    lc_hextab                                0x0800c140   Data          19  _printf_fp_hex.o(.constdata)
    uc_hextab                                0x0800c153   Data          19  _printf_fp_hex.o(.constdata)
    .constdata                               0x0800c168   Section      148  bigflt0.o(.constdata)
    tenpwrs_x                                0x0800c168   Data          60  bigflt0.o(.constdata)
    tenpwrs_i                                0x0800c1a4   Data          64  bigflt0.o(.constdata)
    .conststring                             0x0800c1fc   Section       29  main.o(.conststring)
    .conststring                             0x0800c21c   Section       78  adc_app.o(.conststring)
    .conststring                             0x0800c26c   Section      173  tf_app.o(.conststring)
    c$$dinf                                  0x0800c33c   Section        8  fpconst.o(c$$dinf)
    c$$dmax                                  0x0800c344   Section        8  fpconst.o(c$$dmax)
    locale$$data                             0x0800c34c   Section       28  lc_numeric_c.o(locale$$data)
    __lcnum_c_name                           0x0800c350   Data           2  lc_numeric_c.o(locale$$data)
    __lcnum_c_start                          0x0800c358   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_point                          0x0800c364   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_thousands                      0x0800c366   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_grouping                       0x0800c367   Data           0  lc_numeric_c.o(locale$$data)
    locale$$data                             0x0800c368   Section      272  lc_ctype_c.o(locale$$data)
    __lcnum_c_end                            0x0800c368   Data           0  lc_numeric_c.o(locale$$data)
    __lcctype_c_name                         0x0800c36c   Data           2  lc_ctype_c.o(locale$$data)
    __lcctype_c_start                        0x0800c374   Data           0  lc_ctype_c.o(locale$$data)
    __lcctype_c_end                          0x0800c478   Data           0  lc_ctype_c.o(locale$$data)
    .data                                    0x20000000   Section      645  gd32f470vet6_bsp.o(.data)
    btns                                     0x20000078   Data         168  gd32f470vet6_bsp.o(.data)
    btns_combo                               0x20000120   Data          72  gd32f470vet6_bsp.o(.data)
    temp_old                                 0x20000284   Data           1  gd32f470vet6_bsp.o(.data)
    .data                                    0x20000288   Section       92  main.o(.data)
    .data                                    0x200002e4   Section        4  systick.o(.data)
    delay                                    0x200002e4   Data           4  systick.o(.data)
    .data                                    0x200002e8   Section        8  adc_app.o(.data)
    .data                                    0x200002f0   Section       26  oled.o(.data)
    oled_last_write_time                     0x200002f0   Data           4  oled.o(.data)
    .data                                    0x2000030c   Section       36  sdio_sdcard.o(.data)
    cardtype                                 0x20000314   Data           1  sdio_sdcard.o(.data)
    sd_rca                                   0x20000316   Data           2  sdio_sdcard.o(.data)
    transmode                                0x20000318   Data           4  sdio_sdcard.o(.data)
    totalnumber_bytes                        0x2000031c   Data           4  sdio_sdcard.o(.data)
    stopcondition                            0x20000320   Data           4  sdio_sdcard.o(.data)
    transerror                               0x20000324   Data           1  sdio_sdcard.o(.data)
    transend                                 0x20000328   Data           4  sdio_sdcard.o(.data)
    number_bytes                             0x2000032c   Data           4  sdio_sdcard.o(.data)
    .data                                    0x20000330   Section        6  ff.o(.data)
    FatFs                                    0x20000330   Data           4  ff.o(.data)
    Fsid                                     0x20000334   Data           2  ff.o(.data)
    .data                                    0x20000336   Section        6  gd30ad3344.o(.data)
    .data                                    0x2000033c   Section        4  system_gd32f4xx.o(.data)
    .bss                                     0x20000340   Section     4788  gd32f470vet6_bsp.o(.bss)
    .bss                                     0x200015f4   Section       32  main.o(.bss)
    .bss                                     0x20001614   Section       60  ebtn.o(.bss)
    ebtn_default                             0x20001614   Data          60  ebtn.o(.bss)
    .bss                                     0x20001650   Section       32  sdio_sdcard.o(.bss)
    sd_csd                                   0x20001650   Data          16  sdio_sdcard.o(.bss)
    sd_cid                                   0x20001660   Data          16  sdio_sdcard.o(.bss)
    .bss                                     0x20001670   Section       96  libspace.o(.bss)
    HEAP                                     0x200016d0   Section     4096  startup_gd32f450_470.o(HEAP)
    Heap_Mem                                 0x200016d0   Data        4096  startup_gd32f450_470.o(HEAP)
    STACK                                    0x200026d0   Section     4096  startup_gd32f450_470.o(STACK)
    Stack_Mem                                0x200026d0   Data        4096  startup_gd32f450_470.o(STACK)
    __initial_sp                             0x200036d0   Data           0  startup_gd32f450_470.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEX$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  printf_stubs.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_fini_                               - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _printf_mbtowc                            - Undefined Weak Reference
    _printf_wc                                - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _scanf_longlong                           - Undefined Weak Reference
    _scanf_mbtowc                             - Undefined Weak Reference
    _scanf_string                             - Undefined Weak Reference
    _scanf_wctomb                             - Undefined Weak Reference
    _scanf_wstring                            - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_Size                           0x000001ac   Number         0  startup_gd32f450_470.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_gd32f450_470.o(RESET)
    __Vectors_End                            0x080001ac   Data           0  startup_gd32f450_470.o(RESET)
    __main                                   0x080001ad   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x080001b5   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x080001b5   Thumb Code    44  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x080001b5   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x080001c3   Thumb Code     0  __scatter.o(!!!scatter)
    __decompress                             0x080001e9   Thumb Code    90  __dczerorl2.o(!!dczerorl2)
    __decompress1                            0x080001e9   Thumb Code     0  __dczerorl2.o(!!dczerorl2)
    __scatterload_zeroinit                   0x08000245   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    _printf_n                                0x08000261   Thumb Code     0  _printf_n.o(.ARM.Collect$$_printf_percent$$00000001)
    _printf_percent                          0x08000261   Thumb Code     0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    _printf_p                                0x08000267   Thumb Code     0  _printf_p.o(.ARM.Collect$$_printf_percent$$00000002)
    _printf_f                                0x0800026d   Thumb Code     0  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    _printf_e                                0x08000273   Thumb Code     0  _printf_e.o(.ARM.Collect$$_printf_percent$$00000004)
    _printf_g                                0x08000279   Thumb Code     0  _printf_g.o(.ARM.Collect$$_printf_percent$$00000005)
    _printf_a                                0x0800027f   Thumb Code     0  _printf_a.o(.ARM.Collect$$_printf_percent$$00000006)
    _printf_ll                               0x08000285   Thumb Code     0  _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007)
    _printf_i                                0x0800028f   Thumb Code     0  _printf_i.o(.ARM.Collect$$_printf_percent$$00000008)
    _printf_d                                0x08000295   Thumb Code     0  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    _printf_u                                0x0800029b   Thumb Code     0  _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A)
    _printf_o                                0x080002a1   Thumb Code     0  _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B)
    _printf_x                                0x080002a7   Thumb Code     0  _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C)
    _printf_lli                              0x080002ad   Thumb Code     0  _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D)
    _printf_lld                              0x080002b3   Thumb Code     0  _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E)
    _printf_llu                              0x080002b9   Thumb Code     0  _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F)
    _printf_llo                              0x080002bf   Thumb Code     0  _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010)
    _printf_llx                              0x080002c5   Thumb Code     0  _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011)
    _printf_l                                0x080002cb   Thumb Code     0  _printf_l.o(.ARM.Collect$$_printf_percent$$00000012)
    _printf_c                                0x080002d5   Thumb Code     0  _printf_c.o(.ARM.Collect$$_printf_percent$$00000013)
    _printf_s                                0x080002db   Thumb Code     0  _printf_s.o(.ARM.Collect$$_printf_percent$$00000014)
    _printf_lc                               0x080002e1   Thumb Code     0  _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015)
    _printf_ls                               0x080002e7   Thumb Code     0  _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016)
    _printf_percent_end                      0x080002ed   Thumb Code     0  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    __rt_lib_init                            0x080002f1   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_fp_1                       0x080002f3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000001)
    __rt_lib_init_heap_1                     0x080002f7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    __rt_lib_init_lc_common                  0x080002f7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000F)
    __rt_lib_init_preinit_1                  0x080002f7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_rand_1                     0x080002f7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_user_alloc_1               0x080002f7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_init_lc_collate_1               0x080002fd   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_lc_ctype_2                 0x080002fd   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000012)
    __rt_lib_init_lc_ctype_1                 0x08000309   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_monetary_1              0x08000309   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_numeric_2               0x08000309   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000016)
    __rt_lib_init_alloca_1                   0x08000313   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_argv_1                     0x08000313   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    __rt_lib_init_atexit_1                   0x08000313   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_clock_1                    0x08000313   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_cpp_1                      0x08000313   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_exceptions_1               0x08000313   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_fp_trap_1                  0x08000313   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_getenv_1                   0x08000313   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_lc_numeric_1               0x08000313   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_time_1                  0x08000313   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_return                     0x08000313   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000033)
    __rt_lib_init_signal_1                   0x08000313   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_stdio_1                    0x08000313   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_shutdown                        0x08000315   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x08000317   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_fini_1                 0x08000317   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x08000317   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    __rt_lib_shutdown_heap_1                 0x08000317   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000011)
    __rt_lib_shutdown_return                 0x08000317   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000012)
    __rt_lib_shutdown_signal_1               0x08000317   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_lib_shutdown_stdio_1                0x08000317   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    __rt_lib_shutdown_user_alloc_1           0x08000317   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    __rt_entry                               0x08000319   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x08000319   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x08000319   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x0800031f   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x0800031f   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x08000323   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x08000323   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x0800032b   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x0800032d   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x0800032d   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x08000331   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    Reset_Handler                            0x08000339   Thumb Code     8  startup_gd32f450_470.o(.text)
    ADC_IRQHandler                           0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    CAN0_EWMC_IRQHandler                     0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    CAN0_RX0_IRQHandler                      0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    CAN0_RX1_IRQHandler                      0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    CAN0_TX_IRQHandler                       0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    CAN1_EWMC_IRQHandler                     0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    CAN1_RX0_IRQHandler                      0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    CAN1_RX1_IRQHandler                      0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    CAN1_TX_IRQHandler                       0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    DCI_IRQHandler                           0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA0_Channel0_IRQHandler                 0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA0_Channel1_IRQHandler                 0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA0_Channel2_IRQHandler                 0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA0_Channel3_IRQHandler                 0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA0_Channel4_IRQHandler                 0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA0_Channel5_IRQHandler                 0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA0_Channel6_IRQHandler                 0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA0_Channel7_IRQHandler                 0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA1_Channel0_IRQHandler                 0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA1_Channel1_IRQHandler                 0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA1_Channel2_IRQHandler                 0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA1_Channel3_IRQHandler                 0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA1_Channel4_IRQHandler                 0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA1_Channel5_IRQHandler                 0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA1_Channel6_IRQHandler                 0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA1_Channel7_IRQHandler                 0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    ENET_IRQHandler                          0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    ENET_WKUP_IRQHandler                     0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    EXMC_IRQHandler                          0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    EXTI0_IRQHandler                         0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    EXTI10_15_IRQHandler                     0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    EXTI1_IRQHandler                         0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    EXTI2_IRQHandler                         0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    EXTI3_IRQHandler                         0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    EXTI4_IRQHandler                         0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    EXTI5_9_IRQHandler                       0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    FMC_IRQHandler                           0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    FPU_IRQHandler                           0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    I2C0_ER_IRQHandler                       0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    I2C0_EV_IRQHandler                       0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    I2C1_ER_IRQHandler                       0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    I2C1_EV_IRQHandler                       0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    I2C2_ER_IRQHandler                       0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    I2C2_EV_IRQHandler                       0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    IPA_IRQHandler                           0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    LVD_IRQHandler                           0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    RCU_CTC_IRQHandler                       0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    RTC_Alarm_IRQHandler                     0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    RTC_WKUP_IRQHandler                      0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    SPI0_IRQHandler                          0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    SPI1_IRQHandler                          0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    SPI2_IRQHandler                          0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    SPI3_IRQHandler                          0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    SPI4_IRQHandler                          0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    SPI5_IRQHandler                          0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    TAMPER_STAMP_IRQHandler                  0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER0_BRK_TIMER8_IRQHandler             0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER0_Channel_IRQHandler                0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER0_TRG_CMT_TIMER10_IRQHandler        0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER0_UP_TIMER9_IRQHandler              0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER1_IRQHandler                        0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER2_IRQHandler                        0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER3_IRQHandler                        0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER4_IRQHandler                        0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER5_DAC_IRQHandler                    0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER6_IRQHandler                        0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER7_BRK_TIMER11_IRQHandler            0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER7_Channel_IRQHandler                0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER7_TRG_CMT_TIMER13_IRQHandler        0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER7_UP_TIMER12_IRQHandler             0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    TLI_ER_IRQHandler                        0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    TLI_IRQHandler                           0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    TRNG_IRQHandler                          0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    UART3_IRQHandler                         0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    UART4_IRQHandler                         0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    UART6_IRQHandler                         0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    UART7_IRQHandler                         0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    USART2_IRQHandler                        0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    USART5_IRQHandler                        0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    USBFS_IRQHandler                         0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    USBFS_WKUP_IRQHandler                    0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    USBHS_EP1_In_IRQHandler                  0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    USBHS_EP1_Out_IRQHandler                 0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    USBHS_IRQHandler                         0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    USBHS_WKUP_IRQHandler                    0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    WWDGT_IRQHandler                         0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    __user_initial_stackheap                 0x08000355   Thumb Code    10  startup_gd32f450_470.o(.text)
    vsnprintf                                0x08000379   Thumb Code    48  vsnprintf.o(.text)
    __2sprintf                               0x080003ad   Thumb Code    38  __2sprintf.o(.text)
    __printf                                 0x080003d9   Thumb Code   388  __printf_flags_ss_wp.o(.text)
    __0sscanf                                0x08000561   Thumb Code    52  __0sscanf.o(.text)
    _scanf_int                               0x0800059d   Thumb Code   332  _scanf_int.o(.text)
    memcmp                                   0x080006e9   Thumb Code    88  memcmp.o(.text)
    strlen                                   0x08000741   Thumb Code    62  strlen.o(.text)
    strncmp                                  0x0800077f   Thumb Code   150  strncmp.o(.text)
    __aeabi_memcpy                           0x08000815   Thumb Code     0  rt_memcpy_v6.o(.text)
    __rt_memcpy                              0x08000815   Thumb Code   138  rt_memcpy_v6.o(.text)
    _memcpy_lastbytes                        0x0800087b   Thumb Code     0  rt_memcpy_v6.o(.text)
    __aeabi_memcpy4                          0x0800089f   Thumb Code     0  rt_memcpy_w.o(.text)
    __aeabi_memcpy8                          0x0800089f   Thumb Code     0  rt_memcpy_w.o(.text)
    __rt_memcpy_w                            0x0800089f   Thumb Code   100  rt_memcpy_w.o(.text)
    _memcpy_lastbytes_aligned                0x080008e7   Thumb Code     0  rt_memcpy_w.o(.text)
    __aeabi_memclr                           0x08000903   Thumb Code     0  rt_memclr.o(.text)
    __rt_memclr                              0x08000903   Thumb Code    68  rt_memclr.o(.text)
    _memset                                  0x08000907   Thumb Code     0  rt_memclr.o(.text)
    __aeabi_memclr4                          0x08000947   Thumb Code     0  rt_memclr_w.o(.text)
    __aeabi_memclr8                          0x08000947   Thumb Code     0  rt_memclr_w.o(.text)
    __rt_memclr_w                            0x08000947   Thumb Code    78  rt_memclr_w.o(.text)
    _memset_w                                0x0800094b   Thumb Code     0  rt_memclr_w.o(.text)
    strcmp                                   0x08000995   Thumb Code   128  strcmpv7m.o(.text)
    __use_two_region_memory                  0x08000a15   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x08000a17   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x08000a19   Thumb Code     2  heapauxi.o(.text)
    _printf_pre_padding                      0x08000a1b   Thumb Code    44  _printf_pad.o(.text)
    _printf_post_padding                     0x08000a47   Thumb Code    34  _printf_pad.o(.text)
    _printf_truncate_signed                  0x08000a69   Thumb Code    18  _printf_truncate.o(.text)
    _printf_truncate_unsigned                0x08000a7b   Thumb Code    18  _printf_truncate.o(.text)
    _printf_str                              0x08000a8d   Thumb Code    82  _printf_str.o(.text)
    _printf_int_dec                          0x08000ae1   Thumb Code   104  _printf_dec.o(.text)
    _printf_charcount                        0x08000b59   Thumb Code    40  _printf_charcount.o(.text)
    __lib_sel_fp_printf                      0x08000b81   Thumb Code     2  _printf_fp_dec.o(.text)
    _printf_fp_dec_real                      0x08000d33   Thumb Code   620  _printf_fp_dec.o(.text)
    _printf_char_common                      0x08000fab   Thumb Code    32  _printf_char_common.o(.text)
    _sputc                                   0x08000fd1   Thumb Code    10  _sputc.o(.text)
    _snputc                                  0x08000fdb   Thumb Code    16  _snputc.o(.text)
    _printf_wctomb                           0x08000fed   Thumb Code   182  _printf_wctomb.o(.text)
    _printf_longlong_dec                     0x080010a9   Thumb Code   108  _printf_longlong_dec.o(.text)
    _printf_longlong_oct                     0x08001125   Thumb Code    66  _printf_oct_int_ll.o(.text)
    _printf_int_oct                          0x08001167   Thumb Code    24  _printf_oct_int_ll.o(.text)
    _printf_ll_oct                           0x0800117f   Thumb Code    12  _printf_oct_int_ll.o(.text)
    _printf_longlong_hex                     0x08001195   Thumb Code    86  _printf_hex_int_ll_ptr.o(.text)
    _printf_int_hex                          0x080011eb   Thumb Code    28  _printf_hex_int_ll_ptr.o(.text)
    _printf_ll_hex                           0x08001207   Thumb Code    12  _printf_hex_int_ll_ptr.o(.text)
    _printf_hex_ptr                          0x08001213   Thumb Code    18  _printf_hex_int_ll_ptr.o(.text)
    _chval                                   0x08001229   Thumb Code    28  _chval.o(.text)
    _scanf_really_real                       0x08001491   Thumb Code   684  scanf_fp.o(.text)
    __vfscanf_char                           0x08001749   Thumb Code    24  scanf_char.o(.text)
    _sgetc                                   0x08001769   Thumb Code    30  _sgetc.o(.text)
    _sbackspace                              0x08001787   Thumb Code    34  _sgetc.o(.text)
    __rt_locale                              0x080017a9   Thumb Code     8  rt_locale_intlibspace.o(.text)
    __aeabi_errno_addr                       0x080017b1   Thumb Code     8  rt_errno_addr_intlibspace.o(.text)
    __errno$intlibspace                      0x080017b1   Thumb Code     0  rt_errno_addr_intlibspace.o(.text)
    __rt_errno_addr$intlibspace              0x080017b1   Thumb Code     0  rt_errno_addr_intlibspace.o(.text)
    _ll_udiv10                               0x080017b9   Thumb Code   138  lludiv10.o(.text)
    isspace                                  0x08001843   Thumb Code    18  isspace.o(.text)
    _printf_int_common                       0x08001855   Thumb Code   178  _printf_intcommon.o(.text)
    _printf_fp_hex_real                      0x08001909   Thumb Code   756  _printf_fp_hex.o(.text)
    _printf_fp_infnan                        0x08001c05   Thumb Code   112  _printf_fp_infnan.o(.text)
    _printf_cs_common                        0x08001c85   Thumb Code    20  _printf_char.o(.text)
    _printf_char                             0x08001c99   Thumb Code    16  _printf_char.o(.text)
    _printf_string                           0x08001ca9   Thumb Code     8  _printf_char.o(.text)
    _printf_lcs_common                       0x08001cb1   Thumb Code    20  _printf_wchar.o(.text)
    _printf_wchar                            0x08001cc5   Thumb Code    16  _printf_wchar.o(.text)
    _printf_wstring                          0x08001cd5   Thumb Code     8  _printf_wchar.o(.text)
    __vfscanf                                0x08001cdd   Thumb Code   880  _scanf.o(.text)
    _btod_etento                             0x08002051   Thumb Code   224  bigflt0.o(.text)
    _wcrtomb                                 0x08002135   Thumb Code    64  _wcrtomb.o(.text)
    __user_libspace                          0x08002175   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x08002175   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x08002175   Thumb Code     0  libspace.o(.text)
    __user_setup_stackheap                   0x0800217d   Thumb Code    74  sys_stackheap_outer.o(.text)
    __rt_ctype_table                         0x080021c9   Thumb Code    16  rt_ctype_table.o(.text)
    __read_errno                             0x080021d9   Thumb Code    10  _rserrno.o(.text)
    __set_errno                              0x080021e3   Thumb Code    12  _rserrno.o(.text)
    _scanf_really_hex_real                   0x080021f1   Thumb Code   786  scanf_hexfp.o(.text)
    _scanf_really_infnan                     0x08002511   Thumb Code   292  scanf_infnan.o(.text)
    exit                                     0x08002645   Thumb Code    18  exit.o(.text)
    __aeabi_llsl                             0x08002657   Thumb Code     0  llshl.o(.text)
    _ll_shift_l                              0x08002657   Thumb Code    38  llshl.o(.text)
    _sys_exit                                0x0800267d   Thumb Code     8  sys_exit.o(.text)
    __I$use$semihosting                      0x08002689   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x08002689   Thumb Code     2  use_no_semi.o(.text)
    __semihosting_library_function           0x0800268b   Thumb Code     0  indicate_semi.o(.text)
    _btod_d2e                                0x0800268b   Thumb Code    62  btod.o(CL$$btod_d2e)
    _d2e_denorm_low                          0x080026c9   Thumb Code    70  btod.o(CL$$btod_d2e_denorm_low)
    _d2e_norm_op1                            0x0800270f   Thumb Code    96  btod.o(CL$$btod_d2e_norm_op1)
    __btod_div_common                        0x0800276f   Thumb Code   696  btod.o(CL$$btod_div_common)
    _e2d                                     0x08002aa9   Thumb Code   122  btod.o(CL$$btod_e2d)
    _e2e                                     0x08002b2d   Thumb Code   220  btod.o(CL$$btod_e2e)
    _btod_ediv                               0x08002c09   Thumb Code    42  btod.o(CL$$btod_ediv)
    _btod_edivd                              0x08002c33   Thumb Code    42  btod.o(CL$$btod_edivd)
    _btod_emul                               0x08002c5d   Thumb Code    42  btod.o(CL$$btod_emul)
    _btod_emuld                              0x08002c87   Thumb Code    42  btod.o(CL$$btod_emuld)
    __btod_mult_common                       0x08002cb1   Thumb Code   580  btod.o(CL$$btod_mult_common)
    AD3344_Send_Data                         0x08002ef5   Thumb Code    16  gd30ad3344.o(i.AD3344_Send_Data)
    AdcTask                                  0x08003019   Thumb Code    62  adc_app.o(i.AdcTask)
    BcdToDec                                 0x0800306d   Thumb Code    20  gd32f470vet6_bsp.o(i.BcdToDec)
    BtnEventCallback                         0x08003081   Thumb Code   204  btn_app.o(i.BtnEventCallback)
    BtnTask                                  0x0800326d   Thumb Code    12  btn_app.o(i.BtnTask)
    BusFault_Handler                         0x0800327d   Thumb Code     4  gd32f4xx_it.o(i.BusFault_Handler)
    CmdGetData                               0x08003281   Thumb Code    94  command_proc.o(i.CmdGetData)
    CmdProc                                  0x08003309   Thumb Code   160  command_proc.o(i.CmdProc)
    CmdStartSample                           0x08003465   Thumb Code    14  command_proc.o(i.CmdStartSample)
    CmdStopSample                            0x08003479   Thumb Code    16  command_proc.o(i.CmdStopSample)
    Cmd_GetDeviceId                          0x08003499   Thumb Code    12  command_proc.o(i.Cmd_GetDeviceId)
    Cmd_GetRatio                             0x080034c1   Thumb Code    94  command_proc.o(i.Cmd_GetRatio)
    Cmd_GetRtc                               0x0800355d   Thumb Code    36  command_proc.o(i.Cmd_GetRtc)
    Cmd_SetLimit                             0x080035c5   Thumb Code   144  command_proc.o(i.Cmd_SetLimit)
    Cmd_SetRatio                             0x080036c5   Thumb Code   128  command_proc.o(i.Cmd_SetRatio)
    Cmd_SetRtc                               0x08003785   Thumb Code    90  command_proc.o(i.Cmd_SetRtc)
    DebugMon_Handler                         0x08003821   Thumb Code     4  gd32f4xx_it.o(i.DebugMon_Handler)
    DecToBcd                                 0x08003825   Thumb Code    26  gd32f470vet6_bsp.o(i.DecToBcd)
    Flash_ReadDeviceId                       0x080038d9   Thumb Code    16  flash_app.o(i.Flash_ReadDeviceId)
    Gd30Task                                 0x08003981   Thumb Code   192  adc_app.o(i.Gd30Task)
    HardFault_Handler                        0x08003a95   Thumb Code     4  gd32f4xx_it.o(i.HardFault_Handler)
    LedDisp                                  0x08003bf5   Thumb Code   126  gd32f470vet6_bsp.o(i.LedDisp)
    LedTask                                  0x08003cb5   Thumb Code    10  led_app.o(i.LedTask)
    MemManage_Handler                        0x08003cc5   Thumb Code     4  gd32f4xx_it.o(i.MemManage_Handler)
    NMI_Handler                              0x08003cc9   Thumb Code     4  gd32f4xx_it.o(i.NMI_Handler)
    OLED_Clear                               0x08003cf5   Thumb Code    62  oled.o(i.OLED_Clear)
    OLED_Flush_buffer                        0x08003d35   Thumb Code    32  oled.o(i.OLED_Flush_buffer)
    OLED_Init                                0x08003d5d   Thumb Code    46  oled.o(i.OLED_Init)
    OLED_Set_Position                        0x08003d91   Thumb Code    40  oled.o(i.OLED_Set_Position)
    OLED_ShowChar                            0x08003db9   Thumb Code   186  oled.o(i.OLED_ShowChar)
    OLED_ShowStr                             0x08003e85   Thumb Code    66  oled.o(i.OLED_ShowStr)
    OLED_Write_cmd                           0x08003ec9   Thumb Code   282  oled.o(i.OLED_Write_cmd)
    OLED_Write_data                          0x08003ff1   Thumb Code    12  oled.o(i.OLED_Write_data)
    OLED_Write_data_batch                    0x08003ffd   Thumb Code   304  oled.o(i.OLED_Write_data_batch)
    OLED_Write_data_buffered                 0x08004139   Thumb Code    86  oled.o(i.OLED_Write_data_buffered)
    OledDrawStr                              0x08004229   Thumb Code    60  gd32f470vet6_bsp.o(i.OledDrawStr)
    OledTask                                 0x08004305   Thumb Code    42  oled_app.o(i.OledTask)
    PendSV_Handler                           0x08004345   Thumb Code     4  gd32f4xx_it.o(i.PendSV_Handler)
    ReadRtc                                  0x08004349   Thumb Code   106  gd32f470vet6_bsp.o(i.ReadRtc)
    RtcTask                                  0x08004539   Thumb Code    10  rtc_app.o(i.RtcTask)
    SDIO_IRQHandler                          0x08004549   Thumb Code     8  gd32f4xx_it.o(i.SDIO_IRQHandler)
    SVC_Handler                              0x08004551   Thumb Code     4  gd32f4xx_it.o(i.SVC_Handler)
    SampleProc                               0x08004555   Thumb Code   126  adc_app.o(i.SampleProc)
    SampleTask                               0x080045e5   Thumb Code    46  adc_app.o(i.SampleTask)
    SetRtc                                   0x08004621   Thumb Code   130  gd32f470vet6_bsp.o(i.SetRtc)
    SysInit                                  0x080046ad   Thumb Code    48  gd32f470vet6_bsp.o(i.SysInit)
    SysTick_Handler                          0x080046dd   Thumb Code    18  gd32f4xx_it.o(i.SysTick_Handler)
    SystemInit                               0x080046f5   Thumb Code   364  system_gd32f4xx.o(i.SystemInit)
    TF_WriteConfig                           0x08004871   Thumb Code   244  tf_app.o(i.TF_WriteConfig)
    TF_WriteLimit                            0x08004979   Thumb Code   250  tf_app.o(i.TF_WriteLimit)
    TF_WriteRatio                            0x08004b11   Thumb Code   180  tf_app.o(i.TF_WriteRatio)
    TaskExeution                             0x08004bd9   Thumb Code    78  scheduler.o(i.TaskExeution)
    USART0_IRQHandler                        0x08004cbd   Thumb Code   138  gd32f4xx_it.o(i.USART0_IRQHandler)
    USART1_IRQHandler                        0x08004d61   Thumb Code   138  gd32f4xx_it.o(i.USART1_IRQHandler)
    UsageFault_Handler                       0x08004e05   Thumb Code     4  gd32f4xx_it.o(i.UsageFault_Handler)
    Usart0Printf                             0x08004f29   Thumb Code   102  gd32f470vet6_bsp.o(i.Usart0Printf)
    Usart0Task                               0x08004f99   Thumb Code    38  usart_app.o(i.Usart0Task)
    Usart1Printf                             0x08005101   Thumb Code   124  gd32f470vet6_bsp.o(i.Usart1Printf)
    Usart1Task                               0x08005185   Thumb Code    36  usart_app.o(i.Usart1Task)
    __ARM_fpclassify                         0x08005211   Thumb Code    48  fpclassify.o(i.__ARM_fpclassify)
    __hardfp___mathlib_tofloat               0x08005241   Thumb Code   232  narrow.o(i.__hardfp___mathlib_tofloat)
    __hardfp_ldexp                           0x08005339   Thumb Code   200  ldexp.o(i.__hardfp_ldexp)
    __mathlib_dbl_overflow                   0x08005409   Thumb Code    24  dunder.o(i.__mathlib_dbl_overflow)
    __mathlib_dbl_underflow                  0x08005429   Thumb Code    24  dunder.o(i.__mathlib_dbl_underflow)
    __mathlib_narrow                         0x08005449   Thumb Code    18  narrow.o(i.__mathlib_narrow)
    __support_ldexp                          0x0800545b   Thumb Code    20  ldexp.o(i.__support_ldexp)
    _is_digit                                0x0800546f   Thumb Code    14  __printf_wp.o(i._is_digit)
    ad3344_init                              0x08005499   Thumb Code    64  gd30ad3344.o(i.ad3344_init)
    ad3344_read_data16                       0x080054e1   Thumb Code    86  gd30ad3344.o(i.ad3344_read_data16)
    ad3344_reset                             0x0800553d   Thumb Code    12  gd30ad3344.o(i.ad3344_reset)
    ad3344_spi_txrx16bit                     0x08005549   Thumb Code   104  gd30ad3344.o(i.ad3344_spi_txrx16bit)
    adc_calibration_enable                   0x080055b5   Thumb Code    42  gd32f4xx_adc.o(i.adc_calibration_enable)
    adc_channel_length_config                0x080055df   Thumb Code    82  gd32f4xx_adc.o(i.adc_channel_length_config)
    adc_clock_config                         0x08005631   Thumb Code    28  gd32f4xx_adc.o(i.adc_clock_config)
    adc_data_alignment_config                0x08005655   Thumb Code    22  gd32f4xx_adc.o(i.adc_data_alignment_config)
    adc_dma_mode_enable                      0x0800566b   Thumb Code    10  gd32f4xx_adc.o(i.adc_dma_mode_enable)
    adc_dma_request_after_last_enable        0x08005675   Thumb Code    10  gd32f4xx_adc.o(i.adc_dma_request_after_last_enable)
    adc_enable                               0x0800567f   Thumb Code    18  gd32f4xx_adc.o(i.adc_enable)
    adc_external_trigger_config              0x08005691   Thumb Code    52  gd32f4xx_adc.o(i.adc_external_trigger_config)
    adc_external_trigger_source_config       0x080056c5   Thumb Code    48  gd32f4xx_adc.o(i.adc_external_trigger_source_config)
    adc_routine_channel_config               0x080056f5   Thumb Code   172  gd32f4xx_adc.o(i.adc_routine_channel_config)
    adc_software_trigger_enable              0x080057a1   Thumb Code    36  gd32f4xx_adc.o(i.adc_software_trigger_enable)
    adc_special_function_config              0x080057c5   Thumb Code    90  gd32f4xx_adc.o(i.adc_special_function_config)
    adc_sync_mode_config                     0x08005821   Thumb Code    28  gd32f4xx_adc.o(i.adc_sync_mode_config)
    clust2sect                               0x08005d85   Thumb Code    26  ff.o(i.clust2sect)
    delay_1ms                                0x0800619d   Thumb Code    16  systick.o(i.delay_1ms)
    delay_decrement                          0x080061b1   Thumb Code    18  systick.o(i.delay_decrement)
    delay_us                                 0x080061c9   Thumb Code    26  gd30ad3344.o(i.delay_us)
    disk_initialize                          0x08006601   Thumb Code   134  diskio.o(i.disk_initialize)
    disk_ioctl                               0x08006687   Thumb Code     6  diskio.o(i.disk_ioctl)
    disk_read                                0x0800668d   Thumb Code    80  diskio.o(i.disk_read)
    disk_status                              0x080066dd   Thumb Code    12  diskio.o(i.disk_status)
    disk_write                               0x080066e9   Thumb Code    80  diskio.o(i.disk_write)
    dma_channel_disable                      0x08006739   Thumb Code    32  gd32f4xx_dma.o(i.dma_channel_disable)
    dma_channel_enable                       0x08006759   Thumb Code    32  gd32f4xx_dma.o(i.dma_channel_enable)
    dma_channel_subperipheral_select         0x08006779   Thumb Code    38  gd32f4xx_dma.o(i.dma_channel_subperipheral_select)
    dma_circulation_disable                  0x0800679f   Thumb Code    32  gd32f4xx_dma.o(i.dma_circulation_disable)
    dma_circulation_enable                   0x080067bf   Thumb Code    32  gd32f4xx_dma.o(i.dma_circulation_enable)
    dma_deinit                               0x080067df   Thumb Code   166  gd32f4xx_dma.o(i.dma_deinit)
    dma_flag_clear                           0x08006885   Thumb Code    62  gd32f4xx_dma.o(i.dma_flag_clear)
    dma_flag_get                             0x080068c3   Thumb Code    76  gd32f4xx_dma.o(i.dma_flag_get)
    dma_flow_controller_config               0x0800690f   Thumb Code    64  gd32f4xx_dma.o(i.dma_flow_controller_config)
    dma_memory_address_config                0x0800694f   Thumb Code    32  gd32f4xx_dma.o(i.dma_memory_address_config)
    dma_multi_data_mode_init                 0x08006971   Thumb Code   352  gd32f4xx_dma.o(i.dma_multi_data_mode_init)
    dma_single_data_mode_init                0x08006b89   Thumb Code   340  gd32f4xx_dma.o(i.dma_single_data_mode_init)
    dma_single_data_para_struct_init         0x08006ce1   Thumb Code    34  gd32f4xx_dma.o(i.dma_single_data_para_struct_init)
    dma_transfer_number_config               0x08006db9   Thumb Code    16  gd32f4xx_dma.o(i.dma_transfer_number_config)
    dma_transfer_number_get                  0x08006dc9   Thumb Code    16  gd32f4xx_dma.o(i.dma_transfer_number_get)
    ebtn_combo_btn_add_btn                   0x08006dd9   Thumb Code    32  ebtn.o(i.ebtn_combo_btn_add_btn)
    ebtn_combo_btn_add_btn_by_idx            0x08006df9   Thumb Code    32  ebtn.o(i.ebtn_combo_btn_add_btn_by_idx)
    ebtn_get_btn_index_by_key_id             0x08006e19   Thumb Code    66  ebtn.o(i.ebtn_get_btn_index_by_key_id)
    ebtn_init                                0x08006eb9   Thumb Code    88  ebtn.o(i.ebtn_init)
    ebtn_process                             0x08006f15   Thumb Code    26  ebtn.o(i.ebtn_process)
    ebtn_process_with_curr_state             0x08006fe9   Thumb Code   444  ebtn.o(i.ebtn_process_with_curr_state)
    ebtn_set_combo_suppress_threshold        0x080071a9   Thumb Code     6  ebtn.o(i.ebtn_set_combo_suppress_threshold)
    ebtn_set_config                          0x080071b5   Thumb Code     8  ebtn.o(i.ebtn_set_config)
    f_close                                  0x080071c7   Thumb Code    22  ff.o(i.f_close)
    f_mount                                  0x080071dd   Thumb Code    38  ff.o(i.f_mount)
    f_open                                   0x08007209   Thumb Code   372  ff.o(i.f_open)
    f_read                                   0x0800737d   Thumb Code   462  ff.o(i.f_read)
    f_sync                                   0x0800754b   Thumb Code   184  ff.o(i.f_sync)
    f_write                                  0x08007603   Thumb Code   526  ff.o(i.f_write)
    ff_convert                               0x08007811   Thumb Code    20  unicode.o(i.ff_convert)
    ff_wtoupper                              0x08007825   Thumb Code    60  unicode.o(i.ff_wtoupper)
    frexp                                    0x08007989   Thumb Code   118  frexp.o(i.frexp)
    gen_numname                              0x08007a15   Thumb Code   190  ff.o(i.gen_numname)
    get_fat                                  0x08007ad3   Thumb Code   228  ff.o(i.get_fat)
    get_fattime                              0x08007bb7   Thumb Code     4  diskio.o(i.get_fattime)
    gpio_af_set                              0x08007bbb   Thumb Code    94  gd32f4xx_gpio.o(i.gpio_af_set)
    gpio_bit_reset                           0x08007c19   Thumb Code     4  gd32f4xx_gpio.o(i.gpio_bit_reset)
    gpio_bit_set                             0x08007c1d   Thumb Code     4  gd32f4xx_gpio.o(i.gpio_bit_set)
    gpio_bit_write                           0x08007c21   Thumb Code    10  gd32f4xx_gpio.o(i.gpio_bit_write)
    gpio_input_bit_get                       0x08007ca1   Thumb Code    16  gd32f4xx_gpio.o(i.gpio_input_bit_get)
    gpio_mode_set                            0x08007cb1   Thumb Code    78  gd32f4xx_gpio.o(i.gpio_mode_set)
    gpio_output_options_set                  0x08007cff   Thumb Code    66  gd32f4xx_gpio.o(i.gpio_output_options_set)
    i2c_ack_config                           0x08007d41   Thumb Code    16  gd32f4xx_i2c.o(i.i2c_ack_config)
    i2c_clock_config                         0x08007d51   Thumb Code   216  gd32f4xx_i2c.o(i.i2c_clock_config)
    i2c_deinit                               0x08007e35   Thumb Code    84  gd32f4xx_i2c.o(i.i2c_deinit)
    i2c_dma_config                           0x08007e8d   Thumb Code    16  gd32f4xx_i2c.o(i.i2c_dma_config)
    i2c_enable                               0x08007e9d   Thumb Code    10  gd32f4xx_i2c.o(i.i2c_enable)
    i2c_flag_clear                           0x08007ea7   Thumb Code    40  gd32f4xx_i2c.o(i.i2c_flag_clear)
    i2c_flag_get                             0x08007ecf   Thumb Code    30  gd32f4xx_i2c.o(i.i2c_flag_get)
    i2c_master_addressing                    0x08007eed   Thumb Code    20  gd32f4xx_i2c.o(i.i2c_master_addressing)
    i2c_mode_addr_config                     0x08007f01   Thumb Code    28  gd32f4xx_i2c.o(i.i2c_mode_addr_config)
    i2c_start_on_bus                         0x08007f1d   Thumb Code    10  gd32f4xx_i2c.o(i.i2c_start_on_bus)
    i2c_stop_on_bus                          0x08007f27   Thumb Code    10  gd32f4xx_i2c.o(i.i2c_stop_on_bus)
    main                                     0x08007f31   Thumb Code    16  main.o(i.main)
    nvic_irq_enable                          0x08008009   Thumb Code   186  gd32f4xx_misc.o(i.nvic_irq_enable)
    nvic_priority_group_set                  0x080080cd   Thumb Code    10  gd32f4xx_misc.o(i.nvic_priority_group_set)
    pmu_backup_write_enable                  0x080080e1   Thumb Code    14  gd32f4xx_pmu.o(i.pmu_backup_write_enable)
    put_fat                                  0x080084b5   Thumb Code   310  ff.o(i.put_fat)
    rcu_all_reset_flag_clear                 0x080088a5   Thumb Code    14  gd32f4xx_rcu.o(i.rcu_all_reset_flag_clear)
    rcu_clock_freq_get                       0x080088b9   Thumb Code   264  gd32f4xx_rcu.o(i.rcu_clock_freq_get)
    rcu_flag_get                             0x08008a01   Thumb Code    32  gd32f4xx_rcu.o(i.rcu_flag_get)
    rcu_osci_on                              0x08008a25   Thumb Code    32  gd32f4xx_rcu.o(i.rcu_osci_on)
    rcu_osci_stab_wait                       0x08008a49   Thumb Code   342  gd32f4xx_rcu.o(i.rcu_osci_stab_wait)
    rcu_periph_clock_enable                  0x08008ba5   Thumb Code    32  gd32f4xx_rcu.o(i.rcu_periph_clock_enable)
    rcu_periph_reset_disable                 0x08008bc9   Thumb Code    32  gd32f4xx_rcu.o(i.rcu_periph_reset_disable)
    rcu_periph_reset_enable                  0x08008bed   Thumb Code    32  gd32f4xx_rcu.o(i.rcu_periph_reset_enable)
    rcu_rtc_clock_config                     0x08008c11   Thumb Code    18  gd32f4xx_rcu.o(i.rcu_rtc_clock_config)
    rtc_current_time_get                     0x08008c91   Thumb Code    96  gd32f4xx_rtc.o(i.rtc_current_time_get)
    rtc_init                                 0x08008cf5   Thumb Code   190  gd32f4xx_rtc.o(i.rtc_init)
    rtc_init_mode_enter                      0x08008db9   Thumb Code    66  gd32f4xx_rtc.o(i.rtc_init_mode_enter)
    rtc_init_mode_exit                       0x08008e01   Thumb Code    14  gd32f4xx_rtc.o(i.rtc_init_mode_exit)
    rtc_register_sync_wait                   0x08008e15   Thumb Code    92  gd32f4xx_rtc.o(i.rtc_register_sync_wait)
    sd_block_read                            0x08008e75   Thumb Code   500  sdio_sdcard.o(i.sd_block_read)
    sd_block_write                           0x0800908d   Thumb Code   760  sdio_sdcard.o(i.sd_block_write)
    sd_bus_mode_config                       0x080093ad   Thumb Code   144  sdio_sdcard.o(i.sd_bus_mode_config)
    sd_card_information_get                  0x0800953d   Thumb Code   686  sdio_sdcard.o(i.sd_card_information_get)
    sd_card_init                             0x080097fd   Thumb Code   268  sdio_sdcard.o(i.sd_card_init)
    sd_card_select_deselect                  0x08009919   Thumb Code    38  sdio_sdcard.o(i.sd_card_select_deselect)
    sd_cardstatus_get                        0x080099f9   Thumb Code    66  sdio_sdcard.o(i.sd_cardstatus_get)
    sd_init                                  0x08009a59   Thumb Code    70  sdio_sdcard.o(i.sd_init)
    sd_interrupts_process                    0x08009aa1   Thumb Code   286  sdio_sdcard.o(i.sd_interrupts_process)
    sd_multiblocks_read                      0x08009bd1   Thumb Code   632  sdio_sdcard.o(i.sd_multiblocks_read)
    sd_multiblocks_write                     0x08009e6d   Thumb Code   878  sdio_sdcard.o(i.sd_multiblocks_write)
    sd_power_on                              0x0800a205   Thumb Code   290  sdio_sdcard.o(i.sd_power_on)
    sd_transfer_mode_config                  0x0800a48d   Thumb Code    20  sdio_sdcard.o(i.sd_transfer_mode_config)
    sd_transfer_stop                         0x0800a4a5   Thumb Code    36  sdio_sdcard.o(i.sd_transfer_stop)
    sdio_bus_mode_set                        0x0800a4c9   Thumb Code    22  gd32f4xx_sdio.o(i.sdio_bus_mode_set)
    sdio_clock_config                        0x0800a4e5   Thumb Code    44  gd32f4xx_sdio.o(i.sdio_clock_config)
    sdio_clock_enable                        0x0800a519   Thumb Code    14  gd32f4xx_sdio.o(i.sdio_clock_enable)
    sdio_command_index_get                   0x0800a52d   Thumb Code     8  gd32f4xx_sdio.o(i.sdio_command_index_get)
    sdio_command_response_config             0x0800a539   Thumb Code    52  gd32f4xx_sdio.o(i.sdio_command_response_config)
    sdio_csm_enable                          0x0800a571   Thumb Code    14  gd32f4xx_sdio.o(i.sdio_csm_enable)
    sdio_data_config                         0x0800a585   Thumb Code    54  gd32f4xx_sdio.o(i.sdio_data_config)
    sdio_data_read                           0x0800a5c1   Thumb Code     6  gd32f4xx_sdio.o(i.sdio_data_read)
    sdio_data_transfer_config                0x0800a5cd   Thumb Code    24  gd32f4xx_sdio.o(i.sdio_data_transfer_config)
    sdio_data_write                          0x0800a5e9   Thumb Code     6  gd32f4xx_sdio.o(i.sdio_data_write)
    sdio_deinit                              0x0800a5f5   Thumb Code    20  gd32f4xx_sdio.o(i.sdio_deinit)
    sdio_dma_disable                         0x0800a609   Thumb Code    14  gd32f4xx_sdio.o(i.sdio_dma_disable)
    sdio_dma_enable                          0x0800a61d   Thumb Code    14  gd32f4xx_sdio.o(i.sdio_dma_enable)
    sdio_dsm_disable                         0x0800a631   Thumb Code    14  gd32f4xx_sdio.o(i.sdio_dsm_disable)
    sdio_dsm_enable                          0x0800a645   Thumb Code    14  gd32f4xx_sdio.o(i.sdio_dsm_enable)
    sdio_flag_clear                          0x0800a659   Thumb Code     6  gd32f4xx_sdio.o(i.sdio_flag_clear)
    sdio_flag_get                            0x0800a665   Thumb Code    16  gd32f4xx_sdio.o(i.sdio_flag_get)
    sdio_hardware_clock_disable              0x0800a679   Thumb Code    14  gd32f4xx_sdio.o(i.sdio_hardware_clock_disable)
    sdio_interrupt_disable                   0x0800a68d   Thumb Code    12  gd32f4xx_sdio.o(i.sdio_interrupt_disable)
    sdio_interrupt_enable                    0x0800a69d   Thumb Code    12  gd32f4xx_sdio.o(i.sdio_interrupt_enable)
    sdio_interrupt_flag_clear                0x0800a6ad   Thumb Code     6  gd32f4xx_sdio.o(i.sdio_interrupt_flag_clear)
    sdio_interrupt_flag_get                  0x0800a6b9   Thumb Code    16  gd32f4xx_sdio.o(i.sdio_interrupt_flag_get)
    sdio_power_state_get                     0x0800a6cd   Thumb Code     6  gd32f4xx_sdio.o(i.sdio_power_state_get)
    sdio_power_state_set                     0x0800a6d9   Thumb Code     6  gd32f4xx_sdio.o(i.sdio_power_state_set)
    sdio_response_get                        0x0800a6e5   Thumb Code    56  gd32f4xx_sdio.o(i.sdio_response_get)
    sdio_wait_type_set                       0x0800a721   Thumb Code    22  gd32f4xx_sdio.o(i.sdio_wait_type_set)
    spi_dma_disable                          0x0800a73d   Thumb Code    22  gd32f4xx_spi.o(i.spi_dma_disable)
    spi_dma_enable                           0x0800a753   Thumb Code    22  gd32f4xx_spi.o(i.spi_dma_enable)
    spi_enable                               0x0800a769   Thumb Code    10  gd32f4xx_spi.o(i.spi_enable)
    spi_flash_buffer_read                    0x0800a775   Thumb Code    80  gd25qxx.o(i.spi_flash_buffer_read)
    spi_flash_init                           0x0800a7c9   Thumb Code    20  gd25qxx.o(i.spi_flash_init)
    spi_flash_send_byte_dma                  0x0800a7e5   Thumb Code   236  gd25qxx.o(i.spi_flash_send_byte_dma)
    spi_i2s_data_receive                     0x0800a8e1   Thumb Code     8  gd32f4xx_spi.o(i.spi_i2s_data_receive)
    spi_i2s_data_transmit                    0x0800a8e9   Thumb Code     4  gd32f4xx_spi.o(i.spi_i2s_data_transmit)
    spi_i2s_flag_get                         0x0800a8ed   Thumb Code    16  gd32f4xx_spi.o(i.spi_i2s_flag_get)
    spi_init                                 0x0800a8fd   Thumb Code    50  gd32f4xx_spi.o(i.spi_init)
    systick_config                           0x0800ab31   Thumb Code    74  systick.o(i.systick_config)
    usart_baudrate_set                       0x0800ab81   Thumb Code   224  gd32f4xx_usart.o(i.usart_baudrate_set)
    usart_data_receive                       0x0800ac69   Thumb Code    10  gd32f4xx_usart.o(i.usart_data_receive)
    usart_data_transmit                      0x0800ac73   Thumb Code     8  gd32f4xx_usart.o(i.usart_data_transmit)
    usart_deinit                             0x0800ac7d   Thumb Code   210  gd32f4xx_usart.o(i.usart_deinit)
    usart_dma_receive_config                 0x0800ad59   Thumb Code    20  gd32f4xx_usart.o(i.usart_dma_receive_config)
    usart_enable                             0x0800ad6d   Thumb Code    10  gd32f4xx_usart.o(i.usart_enable)
    usart_flag_get                           0x0800ad77   Thumb Code    30  gd32f4xx_usart.o(i.usart_flag_get)
    usart_interrupt_disable                  0x0800ad95   Thumb Code    26  gd32f4xx_usart.o(i.usart_interrupt_disable)
    usart_interrupt_enable                   0x0800adaf   Thumb Code    26  gd32f4xx_usart.o(i.usart_interrupt_enable)
    usart_interrupt_flag_get                 0x0800adc9   Thumb Code    56  gd32f4xx_usart.o(i.usart_interrupt_flag_get)
    usart_receive_config                     0x0800ae01   Thumb Code    16  gd32f4xx_usart.o(i.usart_receive_config)
    usart_transmit_config                    0x0800ae11   Thumb Code    16  gd32f4xx_usart.o(i.usart_transmit_config)
    _get_lc_numeric                          0x0800ae4d   Thumb Code    44  lc_numeric_c.o(locale$$code)
    _get_lc_ctype                            0x0800ae79   Thumb Code    44  lc_ctype_c.o(locale$$code)
    __aeabi_d2f                              0x0800aea5   Thumb Code     0  d2f.o(x$fpl$d2f)
    _d2f                                     0x0800aea5   Thumb Code    98  d2f.o(x$fpl$d2f)
    __fpl_dcheck_NaN1                        0x0800af09   Thumb Code    10  dcheck1.o(x$fpl$dcheck1)
    __fpl_dcmp_Inf                           0x0800af19   Thumb Code    24  dcmpi.o(x$fpl$dcmpinf)
    __aeabi_cdcmpeq                          0x0800af31   Thumb Code     0  deqf.o(x$fpl$deqf)
    _dcmpeq                                  0x0800af31   Thumb Code   120  deqf.o(x$fpl$deqf)
    __aeabi_cdcmple                          0x0800afa9   Thumb Code     0  dleqf.o(x$fpl$dleqf)
    _dcmple                                  0x0800afa9   Thumb Code   120  dleqf.o(x$fpl$dleqf)
    __fpl_dcmple_InfNaN                      0x0800b00b   Thumb Code     0  dleqf.o(x$fpl$dleqf)
    __aeabi_dmul                             0x0800b021   Thumb Code     0  dmul.o(x$fpl$dmul)
    _dmul                                    0x0800b021   Thumb Code   332  dmul.o(x$fpl$dmul)
    __fpl_dnaninf                            0x0800b175   Thumb Code   156  dnaninf.o(x$fpl$dnaninf)
    __fpl_dretinf                            0x0800b211   Thumb Code    12  dretinf.o(x$fpl$dretinf)
    __aeabi_cdrcmple                         0x0800b21d   Thumb Code     0  drleqf.o(x$fpl$drleqf)
    _drcmple                                 0x0800b21d   Thumb Code   108  drleqf.o(x$fpl$drleqf)
    __aeabi_f2d                              0x0800b289   Thumb Code     0  f2d.o(x$fpl$f2d)
    _f2d                                     0x0800b289   Thumb Code    86  f2d.o(x$fpl$f2d)
    __fpl_fnaninf                            0x0800b2df   Thumb Code   140  fnaninf.o(x$fpl$fnaninf)
    _fp_init                                 0x0800b36b   Thumb Code    10  fpinit.o(x$fpl$fpinit)
    __fplib_config_fpu_vfp                   0x0800b373   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    __fplib_config_pureend_doubles           0x0800b373   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    __fpl_fretinf                            0x0800b375   Thumb Code    10  fretinf.o(x$fpl$fretinf)
    __ieee_status                            0x0800b37f   Thumb Code     6  istatus.o(x$fpl$ieeestatus)
    _printf_fp_dec                           0x0800b385   Thumb Code     4  printf1.o(x$fpl$printf1)
    _printf_fp_hex                           0x0800b389   Thumb Code     4  printf2.o(x$fpl$printf2)
    __fpl_return_NaN                         0x0800b38d   Thumb Code   100  retnan.o(x$fpl$retnan)
    __ARM_scalbn                             0x0800b3f1   Thumb Code    92  scalbn.o(x$fpl$scalbn)
    _scanf_real                              0x0800b44d   Thumb Code     4  scanf1.o(x$fpl$scanf1)
    _scanf_hex_real                          0x0800b451   Thumb Code     4  scanf2.o(x$fpl$scanf2)
    _scanf_infnan                            0x0800b455   Thumb Code     4  scanf2.o(x$fpl$scanf2)
    __fpl_cmpreturn                          0x0800b459   Thumb Code    48  trapv.o(x$fpl$trapveneer)
    __I$use$fp                               0x0800b488   Number         0  usenofp.o(x$fpl$usenofp)
    F6X8                                     0x0800b4e0   Data         552  oled.o(.constdata)
    Hzk                                      0x0800bcf8   Data         128  oled.o(.constdata)
    Hzb                                      0x0800bd78   Data         512  oled.o(.constdata)
    Region$$Table$$Base                      0x0800c31c   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x0800c33c   Number         0  anon$$obj.o(Region$$Table)
    __aeabi_HUGE_VAL                         0x0800c33c   Data           0  fpconst.o(c$$dinf)
    __aeabi_HUGE_VALL                        0x0800c33c   Data           0  fpconst.o(c$$dinf)
    __aeabi_INFINITY                         0x0800c33c   Data           0  fpconst.o(c$$dinf)
    __dInf                                   0x0800c33c   Data           0  fpconst.o(c$$dinf)
    __huge_val                               0x0800c33c   Data           0  fpconst.o(c$$dinf)
    __dbl_max                                0x0800c344   Data           0  fpconst.o(c$$dmax)
    __ctype                                  0x0800c375   Data           0  lc_ctype_c.o(locale$$data)
    uwTick                                   0x20000000   Data           4  gd32f470vet6_bsp.o(.data)
    schedul_task                             0x20000004   Data         108  gd32f470vet6_bsp.o(.data)
    task_num                                 0x20000070   Data           1  gd32f470vet6_bsp.o(.data)
    ucLed                                    0x20000071   Data           6  gd32f470vet6_bsp.o(.data)
    adc0_voltage                             0x20000168   Data           4  gd32f470vet6_bsp.o(.data)
    usart0_rx_flag                           0x2000016c   Data           1  gd32f470vet6_bsp.o(.data)
    usart1_rx_flag                           0x2000016d   Data           1  gd32f470vet6_bsp.o(.data)
    oled_cmd_buffer                          0x2000016e   Data           2  gd32f470vet6_bsp.o(.data)
    oled_data_buffer                         0x20000170   Data           2  gd32f470vet6_bsp.o(.data)
    oled_batch_buffer                        0x20000172   Data         258  gd32f470vet6_bsp.o(.data)
    oled_buffer_index                        0x20000274   Data           2  gd32f470vet6_bsp.o(.data)
    oled_buffer_threshold                    0x20000276   Data           2  gd32f470vet6_bsp.o(.data)
    prescaler_a                              0x20000278   Data           4  gd32f470vet6_bsp.o(.data)
    prescaler_s                              0x2000027c   Data           4  gd32f470vet6_bsp.o(.data)
    rtcsrc_flag                              0x20000280   Data           4  gd32f470vet6_bsp.o(.data)
    gd30_channels                            0x20000288   Data          64  main.o(.data)
    config_data                              0x200002c8   Data          24  main.o(.data)
    sample_status                            0x200002e0   Data           3  main.o(.data)
    sample_cycle                             0x200002e3   Data           1  main.o(.data)
    gd30_state                               0x200002e8   Data           1  adc_app.o(.data)
    current_channel                          0x200002e9   Data           1  adc_app.o(.data)
    conv_start_time                          0x200002ec   Data           4  adc_app.o(.data)
    initcmd1                                 0x200002f4   Data          22  oled.o(.data)
    sd_scr                                   0x2000030c   Data           8  sdio_sdcard.o(.data)
    ADC_Config                               0x20000336   Data           4  gd30ad3344.o(.data)
    AD3344_CONFIG                            0x2000033a   Data           2  gd30ad3344.o(.data)
    SystemCoreClock                          0x2000033c   Data           4  system_gd32f4xx.o(.data)
    adc0_value                               0x20000340   Data          64  gd32f470vet6_bsp.o(.bss)
    usart0_rx_buffer_dma                     0x20000380   Data        1024  gd32f470vet6_bsp.o(.bss)
    usart0_rx_buffer_proc                    0x20000780   Data        1024  gd32f470vet6_bsp.o(.bss)
    usart1_rx_buffer_dma                     0x20000b80   Data        1024  gd32f470vet6_bsp.o(.bss)
    usart1_rx_buffer_proc                    0x20000f80   Data        1024  gd32f470vet6_bsp.o(.bss)
    ucRtc                                    0x20001380   Data          20  gd32f470vet6_bsp.o(.bss)
    spi1_tx_buffer                           0x20001394   Data          12  gd32f470vet6_bsp.o(.bss)
    spi1_rx_buffer                           0x200013a0   Data          12  gd32f470vet6_bsp.o(.bss)
    spi0_tx_buffer                           0x200013ac   Data          12  gd32f470vet6_bsp.o(.bss)
    spi0_rx_buffer                           0x200013b8   Data          12  gd32f470vet6_bsp.o(.bss)
    fs                                       0x200013c4   Data         560  gd32f470vet6_bsp.o(.bss)
    device_id                                0x200015f4   Data          32  main.o(.bss)
    __libspace_start                         0x20001670   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x200016d0   Data           0  libspace.o(.bss)



==============================================================================

Memory Map of the image

  Image Entry point : 0x080001ad

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x0000c7b8, Max: 0x00080000, ABSOLUTE, COMPRESSED[0x0000c534])

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x0000c478, Max: 0x00080000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x000001ac   Data   RO         2029    RESET               startup_gd32f450_470.o
    0x080001ac   0x080001ac   0x00000008   Code   RO         7570  * !!!main             c_w.l(__main.o)
    0x080001b4   0x080001b4   0x00000034   Code   RO         7993    !!!scatter          c_w.l(__scatter.o)
    0x080001e8   0x080001e8   0x0000005a   Code   RO         7991    !!dczerorl2         c_w.l(__dczerorl2.o)
    0x08000242   0x08000242   0x00000002   PAD
    0x08000244   0x08000244   0x0000001c   Code   RO         7995    !!handler_zi        c_w.l(__scatter_zi.o)
    0x08000260   0x08000260   0x00000000   Code   RO         7545    .ARM.Collect$$_printf_percent$$00000000  c_w.l(_printf_percent.o)
    0x08000260   0x08000260   0x00000006   Code   RO         7631    .ARM.Collect$$_printf_percent$$00000001  c_w.l(_printf_n.o)
    0x08000266   0x08000266   0x00000006   Code   RO         7633    .ARM.Collect$$_printf_percent$$00000002  c_w.l(_printf_p.o)
    0x0800026c   0x0800026c   0x00000006   Code   RO         7544    .ARM.Collect$$_printf_percent$$00000003  c_w.l(_printf_f.o)
    0x08000272   0x08000272   0x00000006   Code   RO         7638    .ARM.Collect$$_printf_percent$$00000004  c_w.l(_printf_e.o)
    0x08000278   0x08000278   0x00000006   Code   RO         7639    .ARM.Collect$$_printf_percent$$00000005  c_w.l(_printf_g.o)
    0x0800027e   0x0800027e   0x00000006   Code   RO         7640    .ARM.Collect$$_printf_percent$$00000006  c_w.l(_printf_a.o)
    0x08000284   0x08000284   0x0000000a   Code   RO         7645    .ARM.Collect$$_printf_percent$$00000007  c_w.l(_printf_ll.o)
    0x0800028e   0x0800028e   0x00000006   Code   RO         7635    .ARM.Collect$$_printf_percent$$00000008  c_w.l(_printf_i.o)
    0x08000294   0x08000294   0x00000006   Code   RO         7636    .ARM.Collect$$_printf_percent$$00000009  c_w.l(_printf_d.o)
    0x0800029a   0x0800029a   0x00000006   Code   RO         7637    .ARM.Collect$$_printf_percent$$0000000A  c_w.l(_printf_u.o)
    0x080002a0   0x080002a0   0x00000006   Code   RO         7634    .ARM.Collect$$_printf_percent$$0000000B  c_w.l(_printf_o.o)
    0x080002a6   0x080002a6   0x00000006   Code   RO         7632    .ARM.Collect$$_printf_percent$$0000000C  c_w.l(_printf_x.o)
    0x080002ac   0x080002ac   0x00000006   Code   RO         7642    .ARM.Collect$$_printf_percent$$0000000D  c_w.l(_printf_lli.o)
    0x080002b2   0x080002b2   0x00000006   Code   RO         7643    .ARM.Collect$$_printf_percent$$0000000E  c_w.l(_printf_lld.o)
    0x080002b8   0x080002b8   0x00000006   Code   RO         7644    .ARM.Collect$$_printf_percent$$0000000F  c_w.l(_printf_llu.o)
    0x080002be   0x080002be   0x00000006   Code   RO         7649    .ARM.Collect$$_printf_percent$$00000010  c_w.l(_printf_llo.o)
    0x080002c4   0x080002c4   0x00000006   Code   RO         7650    .ARM.Collect$$_printf_percent$$00000011  c_w.l(_printf_llx.o)
    0x080002ca   0x080002ca   0x0000000a   Code   RO         7646    .ARM.Collect$$_printf_percent$$00000012  c_w.l(_printf_l.o)
    0x080002d4   0x080002d4   0x00000006   Code   RO         7629    .ARM.Collect$$_printf_percent$$00000013  c_w.l(_printf_c.o)
    0x080002da   0x080002da   0x00000006   Code   RO         7630    .ARM.Collect$$_printf_percent$$00000014  c_w.l(_printf_s.o)
    0x080002e0   0x080002e0   0x00000006   Code   RO         7647    .ARM.Collect$$_printf_percent$$00000015  c_w.l(_printf_lc.o)
    0x080002e6   0x080002e6   0x00000006   Code   RO         7648    .ARM.Collect$$_printf_percent$$00000016  c_w.l(_printf_ls.o)
    0x080002ec   0x080002ec   0x00000004   Code   RO         7641    .ARM.Collect$$_printf_percent$$00000017  c_w.l(_printf_percent_end.o)
    0x080002f0   0x080002f0   0x00000002   Code   RO         7786    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x080002f2   0x080002f2   0x00000004   Code   RO         7787    .ARM.Collect$$libinit$$00000001  c_w.l(libinit2.o)
    0x080002f6   0x080002f6   0x00000000   Code   RO         7790    .ARM.Collect$$libinit$$00000004  c_w.l(libinit2.o)
    0x080002f6   0x080002f6   0x00000000   Code   RO         7793    .ARM.Collect$$libinit$$0000000A  c_w.l(libinit2.o)
    0x080002f6   0x080002f6   0x00000000   Code   RO         7795    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x080002f6   0x080002f6   0x00000000   Code   RO         7797    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x080002f6   0x080002f6   0x00000006   Code   RO         7798    .ARM.Collect$$libinit$$0000000F  c_w.l(libinit2.o)
    0x080002fc   0x080002fc   0x00000000   Code   RO         7800    .ARM.Collect$$libinit$$00000011  c_w.l(libinit2.o)
    0x080002fc   0x080002fc   0x0000000c   Code   RO         7801    .ARM.Collect$$libinit$$00000012  c_w.l(libinit2.o)
    0x08000308   0x08000308   0x00000000   Code   RO         7802    .ARM.Collect$$libinit$$00000013  c_w.l(libinit2.o)
    0x08000308   0x08000308   0x00000000   Code   RO         7804    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x08000308   0x08000308   0x0000000a   Code   RO         7805    .ARM.Collect$$libinit$$00000016  c_w.l(libinit2.o)
    0x08000312   0x08000312   0x00000000   Code   RO         7806    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x08000312   0x08000312   0x00000000   Code   RO         7808    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x08000312   0x08000312   0x00000000   Code   RO         7810    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x08000312   0x08000312   0x00000000   Code   RO         7812    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x08000312   0x08000312   0x00000000   Code   RO         7814    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x08000312   0x08000312   0x00000000   Code   RO         7816    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x08000312   0x08000312   0x00000000   Code   RO         7818    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x08000312   0x08000312   0x00000000   Code   RO         7820    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x08000312   0x08000312   0x00000000   Code   RO         7824    .ARM.Collect$$libinit$$0000002C  c_w.l(libinit2.o)
    0x08000312   0x08000312   0x00000000   Code   RO         7826    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x08000312   0x08000312   0x00000000   Code   RO         7828    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x08000312   0x08000312   0x00000000   Code   RO         7830    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x08000312   0x08000312   0x00000002   Code   RO         7831    .ARM.Collect$$libinit$$00000033  c_w.l(libinit2.o)
    0x08000314   0x08000314   0x00000002   Code   RO         7921    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x08000316   0x08000316   0x00000000   Code   RO         7944    .ARM.Collect$$libshutdown$$00000002  c_w.l(libshutdown2.o)
    0x08000316   0x08000316   0x00000000   Code   RO         7946    .ARM.Collect$$libshutdown$$00000004  c_w.l(libshutdown2.o)
    0x08000316   0x08000316   0x00000000   Code   RO         7948    .ARM.Collect$$libshutdown$$00000006  c_w.l(libshutdown2.o)
    0x08000316   0x08000316   0x00000000   Code   RO         7951    .ARM.Collect$$libshutdown$$00000009  c_w.l(libshutdown2.o)
    0x08000316   0x08000316   0x00000000   Code   RO         7954    .ARM.Collect$$libshutdown$$0000000C  c_w.l(libshutdown2.o)
    0x08000316   0x08000316   0x00000000   Code   RO         7956    .ARM.Collect$$libshutdown$$0000000E  c_w.l(libshutdown2.o)
    0x08000316   0x08000316   0x00000000   Code   RO         7959    .ARM.Collect$$libshutdown$$00000011  c_w.l(libshutdown2.o)
    0x08000316   0x08000316   0x00000002   Code   RO         7960    .ARM.Collect$$libshutdown$$00000012  c_w.l(libshutdown2.o)
    0x08000318   0x08000318   0x00000000   Code   RO         7578    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x08000318   0x08000318   0x00000000   Code   RO         7671    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x08000318   0x08000318   0x00000006   Code   RO         7683    .ARM.Collect$$rtentry$$00000004  c_w.l(__rtentry4.o)
    0x0800031e   0x0800031e   0x00000000   Code   RO         7673    .ARM.Collect$$rtentry$$00000009  c_w.l(__rtentry2.o)
    0x0800031e   0x0800031e   0x00000004   Code   RO         7674    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x08000322   0x08000322   0x00000000   Code   RO         7676    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x08000322   0x08000322   0x00000008   Code   RO         7677    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x0800032a   0x0800032a   0x00000002   Code   RO         7844    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x0800032c   0x0800032c   0x00000000   Code   RO         7882    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x0800032c   0x0800032c   0x00000004   Code   RO         7883    .ARM.Collect$$rtexit$$00000003  c_w.l(rtexit2.o)
    0x08000330   0x08000330   0x00000006   Code   RO         7884    .ARM.Collect$$rtexit$$00000004  c_w.l(rtexit2.o)
    0x08000336   0x08000336   0x00000002   PAD
    0x08000338   0x08000338   0x00000040   Code   RO         2030    .text               startup_gd32f450_470.o
    0x08000378   0x08000378   0x00000034   Code   RO         7516    .text               c_w.l(vsnprintf.o)
    0x080003ac   0x080003ac   0x0000002c   Code   RO         7518    .text               c_w.l(__2sprintf.o)
    0x080003d8   0x080003d8   0x00000188   Code   RO         7541    .text               c_w.l(__printf_flags_ss_wp.o)
    0x08000560   0x08000560   0x0000003c   Code   RO         7546    .text               c_w.l(__0sscanf.o)
    0x0800059c   0x0800059c   0x0000014c   Code   RO         7548    .text               c_w.l(_scanf_int.o)
    0x080006e8   0x080006e8   0x00000058   Code   RO         7550    .text               c_w.l(memcmp.o)
    0x08000740   0x08000740   0x0000003e   Code   RO         7552    .text               c_w.l(strlen.o)
    0x0800077e   0x0800077e   0x00000096   Code   RO         7554    .text               c_w.l(strncmp.o)
    0x08000814   0x08000814   0x0000008a   Code   RO         7556    .text               c_w.l(rt_memcpy_v6.o)
    0x0800089e   0x0800089e   0x00000064   Code   RO         7558    .text               c_w.l(rt_memcpy_w.o)
    0x08000902   0x08000902   0x00000044   Code   RO         7562    .text               c_w.l(rt_memclr.o)
    0x08000946   0x08000946   0x0000004e   Code   RO         7564    .text               c_w.l(rt_memclr_w.o)
    0x08000994   0x08000994   0x00000080   Code   RO         7566    .text               c_w.l(strcmpv7m.o)
    0x08000a14   0x08000a14   0x00000006   Code   RO         7568    .text               c_w.l(heapauxi.o)
    0x08000a1a   0x08000a1a   0x0000004e   Code   RO         7579    .text               c_w.l(_printf_pad.o)
    0x08000a68   0x08000a68   0x00000024   Code   RO         7581    .text               c_w.l(_printf_truncate.o)
    0x08000a8c   0x08000a8c   0x00000052   Code   RO         7583    .text               c_w.l(_printf_str.o)
    0x08000ade   0x08000ade   0x00000002   PAD
    0x08000ae0   0x08000ae0   0x00000078   Code   RO         7585    .text               c_w.l(_printf_dec.o)
    0x08000b58   0x08000b58   0x00000028   Code   RO         7587    .text               c_w.l(_printf_charcount.o)
    0x08000b80   0x08000b80   0x0000041e   Code   RO         7589    .text               c_w.l(_printf_fp_dec.o)
    0x08000f9e   0x08000f9e   0x00000002   PAD
    0x08000fa0   0x08000fa0   0x00000030   Code   RO         7591    .text               c_w.l(_printf_char_common.o)
    0x08000fd0   0x08000fd0   0x0000000a   Code   RO         7593    .text               c_w.l(_sputc.o)
    0x08000fda   0x08000fda   0x00000010   Code   RO         7595    .text               c_w.l(_snputc.o)
    0x08000fea   0x08000fea   0x00000002   PAD
    0x08000fec   0x08000fec   0x000000bc   Code   RO         7597    .text               c_w.l(_printf_wctomb.o)
    0x080010a8   0x080010a8   0x0000007c   Code   RO         7600    .text               c_w.l(_printf_longlong_dec.o)
    0x08001124   0x08001124   0x00000070   Code   RO         7606    .text               c_w.l(_printf_oct_int_ll.o)
    0x08001194   0x08001194   0x00000094   Code   RO         7626    .text               c_w.l(_printf_hex_int_ll_ptr.o)
    0x08001228   0x08001228   0x0000001c   Code   RO         7651    .text               c_w.l(_chval.o)
    0x08001244   0x08001244   0x000004f8   Code   RO         7653    .text               c_w.l(scanf_fp.o)
    0x0800173c   0x0800173c   0x0000002c   Code   RO         7655    .text               c_w.l(scanf_char.o)
    0x08001768   0x08001768   0x00000040   Code   RO         7657    .text               c_w.l(_sgetc.o)
    0x080017a8   0x080017a8   0x00000008   Code   RO         7688    .text               c_w.l(rt_locale_intlibspace.o)
    0x080017b0   0x080017b0   0x00000008   Code   RO         7693    .text               c_w.l(rt_errno_addr_intlibspace.o)
    0x080017b8   0x080017b8   0x0000008a   Code   RO         7695    .text               c_w.l(lludiv10.o)
    0x08001842   0x08001842   0x00000012   Code   RO         7697    .text               c_w.l(isspace.o)
    0x08001854   0x08001854   0x000000b2   Code   RO         7699    .text               c_w.l(_printf_intcommon.o)
    0x08001906   0x08001906   0x00000002   PAD
    0x08001908   0x08001908   0x000002fc   Code   RO         7701    .text               c_w.l(_printf_fp_hex.o)
    0x08001c04   0x08001c04   0x00000080   Code   RO         7704    .text               c_w.l(_printf_fp_infnan.o)
    0x08001c84   0x08001c84   0x0000002c   Code   RO         7708    .text               c_w.l(_printf_char.o)
    0x08001cb0   0x08001cb0   0x0000002c   Code   RO         7710    .text               c_w.l(_printf_wchar.o)
    0x08001cdc   0x08001cdc   0x00000374   Code   RO         7712    .text               c_w.l(_scanf.o)
    0x08002050   0x08002050   0x000000e4   Code   RO         7714    .text               c_w.l(bigflt0.o)
    0x08002134   0x08002134   0x00000040   Code   RO         7739    .text               c_w.l(_wcrtomb.o)
    0x08002174   0x08002174   0x00000008   Code   RO         7766    .text               c_w.l(libspace.o)
    0x0800217c   0x0800217c   0x0000004a   Code   RO         7769    .text               c_w.l(sys_stackheap_outer.o)
    0x080021c6   0x080021c6   0x00000002   PAD
    0x080021c8   0x080021c8   0x00000010   Code   RO         7771    .text               c_w.l(rt_ctype_table.o)
    0x080021d8   0x080021d8   0x00000016   Code   RO         7773    .text               c_w.l(_rserrno.o)
    0x080021ee   0x080021ee   0x00000002   PAD
    0x080021f0   0x080021f0   0x00000320   Code   RO         7775    .text               c_w.l(scanf_hexfp.o)
    0x08002510   0x08002510   0x00000134   Code   RO         7777    .text               c_w.l(scanf_infnan.o)
    0x08002644   0x08002644   0x00000012   Code   RO         7779    .text               c_w.l(exit.o)
    0x08002656   0x08002656   0x00000026   Code   RO         7846    .text               c_w.l(llshl.o)
    0x0800267c   0x0800267c   0x0000000c   Code   RO         7911    .text               c_w.l(sys_exit.o)
    0x08002688   0x08002688   0x00000002   Code   RO         7934    .text               c_w.l(use_no_semi.o)
    0x0800268a   0x0800268a   0x00000000   Code   RO         7936    .text               c_w.l(indicate_semi.o)
    0x0800268a   0x0800268a   0x0000003e   Code   RO         7717    CL$$btod_d2e        c_w.l(btod.o)
    0x080026c8   0x080026c8   0x00000046   Code   RO         7719    CL$$btod_d2e_denorm_low  c_w.l(btod.o)
    0x0800270e   0x0800270e   0x00000060   Code   RO         7718    CL$$btod_d2e_norm_op1  c_w.l(btod.o)
    0x0800276e   0x0800276e   0x00000338   Code   RO         7727    CL$$btod_div_common  c_w.l(btod.o)
    0x08002aa6   0x08002aa6   0x00000002   PAD
    0x08002aa8   0x08002aa8   0x00000084   Code   RO         7725    CL$$btod_e2d        c_w.l(btod.o)
    0x08002b2c   0x08002b2c   0x000000dc   Code   RO         7724    CL$$btod_e2e        c_w.l(btod.o)
    0x08002c08   0x08002c08   0x0000002a   Code   RO         7721    CL$$btod_ediv       c_w.l(btod.o)
    0x08002c32   0x08002c32   0x0000002a   Code   RO         7723    CL$$btod_edivd      c_w.l(btod.o)
    0x08002c5c   0x08002c5c   0x0000002a   Code   RO         7720    CL$$btod_emul       c_w.l(btod.o)
    0x08002c86   0x08002c86   0x0000002a   Code   RO         7722    CL$$btod_emuld      c_w.l(btod.o)
    0x08002cb0   0x08002cb0   0x00000244   Code   RO         7726    CL$$btod_mult_common  c_w.l(btod.o)
    0x08002ef4   0x08002ef4   0x00000010   Code   RO         1890    i.AD3344_Send_Data  gd30ad3344.o
    0x08002f04   0x08002f04   0x0000006c   Code   RO          298    i.AdcDmaInit        gd32f470vet6_bsp.o
    0x08002f70   0x08002f70   0x000000a8   Code   RO          299    i.AdcPeriphInit     gd32f470vet6_bsp.o
    0x08003018   0x08003018   0x00000054   Code   RO          573    i.AdcTask           adc_app.o
    0x0800306c   0x0800306c   0x00000014   Code   RO          300    i.BcdToDec          gd32f470vet6_bsp.o
    0x08003080   0x08003080   0x000000d0   Code   RO          729    i.BtnEventCallback  btn_app.o
    0x08003150   0x08003150   0x000000a4   Code   RO          301    i.BtnGetState       gd32f470vet6_bsp.o
    0x080031f4   0x080031f4   0x00000078   Code   RO          302    i.BtnPeriphInit     gd32f470vet6_bsp.o
    0x0800326c   0x0800326c   0x00000010   Code   RO          730    i.BtnTask           btn_app.o
    0x0800327c   0x0800327c   0x00000004   Code   RO            3    i.BusFault_Handler  gd32f4xx_it.o
    0x08003280   0x08003280   0x00000088   Code   RO          635    i.CmdGetData        command_proc.o
    0x08003308   0x08003308   0x0000015c   Code   RO          636    i.CmdProc           command_proc.o
    0x08003464   0x08003464   0x00000014   Code   RO          637    i.CmdStartSample    command_proc.o
    0x08003478   0x08003478   0x00000020   Code   RO          638    i.CmdStopSample     command_proc.o
    0x08003498   0x08003498   0x00000028   Code   RO          639    i.Cmd_GetDeviceId   command_proc.o
    0x080034c0   0x080034c0   0x0000009c   Code   RO          640    i.Cmd_GetRatio      command_proc.o
    0x0800355c   0x0800355c   0x00000068   Code   RO          641    i.Cmd_GetRtc        command_proc.o
    0x080035c4   0x080035c4   0x00000100   Code   RO          642    i.Cmd_SetLimit      command_proc.o
    0x080036c4   0x080036c4   0x000000c0   Code   RO          643    i.Cmd_SetRatio      command_proc.o
    0x08003784   0x08003784   0x0000009c   Code   RO          644    i.Cmd_SetRtc        command_proc.o
    0x08003820   0x08003820   0x00000004   Code   RO            4    i.DebugMon_Handler  gd32f4xx_it.o
    0x08003824   0x08003824   0x0000001a   Code   RO          303    i.DecToBcd          gd32f470vet6_bsp.o
    0x0800383e   0x0800383e   0x00000002   PAD
    0x08003840   0x08003840   0x00000098   Code   RO          304    i.FlashPeriphInit   gd32f470vet6_bsp.o
    0x080038d8   0x080038d8   0x00000014   Code   RO          709    i.Flash_ReadDeviceId  flash_app.o
    0x080038ec   0x080038ec   0x00000094   Code   RO          305    i.Gd30PeriphInit    gd32f470vet6_bsp.o
    0x08003980   0x08003980   0x000000d8   Code   RO          574    i.Gd30Task          adc_app.o
    0x08003a58   0x08003a58   0x0000003c   Code   RO          306    i.GetDays           gd32f470vet6_bsp.o
    0x08003a94   0x08003a94   0x00000004   Code   RO            5    i.HardFault_Handler  gd32f4xx_it.o
    0x08003a98   0x08003a98   0x00000128   Code   RO         1074    i.I2C_Bus_Reset     oled.o
    0x08003bc0   0x08003bc0   0x00000032   Code   RO          307    i.IsLeapYear        gd32f470vet6_bsp.o
    0x08003bf2   0x08003bf2   0x00000002   PAD
    0x08003bf4   0x08003bf4   0x00000088   Code   RO          308    i.LedDisp           gd32f470vet6_bsp.o
    0x08003c7c   0x08003c7c   0x00000038   Code   RO          309    i.LedPeriphInit     gd32f470vet6_bsp.o
    0x08003cb4   0x08003cb4   0x00000010   Code   RO          615    i.LedTask           led_app.o
    0x08003cc4   0x08003cc4   0x00000004   Code   RO            6    i.MemManage_Handler  gd32f4xx_it.o
    0x08003cc8   0x08003cc8   0x00000004   Code   RO            7    i.NMI_Handler       gd32f4xx_it.o
    0x08003ccc   0x08003ccc   0x00000028   Code   RO          527    i.NVIC_SetPriority  systick.o
    0x08003cf4   0x08003cf4   0x0000003e   Code   RO         1076    i.OLED_Clear        oled.o
    0x08003d32   0x08003d32   0x00000002   PAD
    0x08003d34   0x08003d34   0x00000028   Code   RO         1079    i.OLED_Flush_buffer  oled.o
    0x08003d5c   0x08003d5c   0x00000034   Code   RO         1080    i.OLED_Init         oled.o
    0x08003d90   0x08003d90   0x00000028   Code   RO         1082    i.OLED_Set_Position  oled.o
    0x08003db8   0x08003db8   0x000000cc   Code   RO         1083    i.OLED_ShowChar     oled.o
    0x08003e84   0x08003e84   0x00000042   Code   RO         1089    i.OLED_ShowStr      oled.o
    0x08003ec6   0x08003ec6   0x00000002   PAD
    0x08003ec8   0x08003ec8   0x00000128   Code   RO         1090    i.OLED_Write_cmd    oled.o
    0x08003ff0   0x08003ff0   0x0000000c   Code   RO         1091    i.OLED_Write_data   oled.o
    0x08003ffc   0x08003ffc   0x0000013c   Code   RO         1092    i.OLED_Write_data_batch  oled.o
    0x08004138   0x08004138   0x0000006c   Code   RO         1093    i.OLED_Write_data_buffered  oled.o
    0x080041a4   0x080041a4   0x00000084   Code   RO          310    i.OledDmaInit       gd32f470vet6_bsp.o
    0x08004228   0x08004228   0x0000003c   Code   RO          311    i.OledDrawStr       gd32f470vet6_bsp.o
    0x08004264   0x08004264   0x000000a0   Code   RO          312    i.OledPeriphInit    gd32f470vet6_bsp.o
    0x08004304   0x08004304   0x00000040   Code   RO          755    i.OledTask          oled_app.o
    0x08004344   0x08004344   0x00000004   Code   RO            8    i.PendSV_Handler    gd32f4xx_it.o
    0x08004348   0x08004348   0x0000006a   Code   RO          313    i.ReadRtc           gd32f470vet6_bsp.o
    0x080043b2   0x080043b2   0x00000002   PAD
    0x080043b4   0x080043b4   0x00000010   Code   RO          314    i.Rs485RxMode       gd32f470vet6_bsp.o
    0x080043c4   0x080043c4   0x00000010   Code   RO          315    i.Rs485TxMode       gd32f470vet6_bsp.o
    0x080043d4   0x080043d4   0x00000124   Code   RO          316    i.RtcPeriphInit     gd32f470vet6_bsp.o
    0x080044f8   0x080044f8   0x00000040   Code   RO          317    i.RtcPreConfig      gd32f470vet6_bsp.o
    0x08004538   0x08004538   0x00000010   Code   RO          775    i.RtcTask           rtc_app.o
    0x08004548   0x08004548   0x00000008   Code   RO            9    i.SDIO_IRQHandler   gd32f4xx_it.o
    0x08004550   0x08004550   0x00000004   Code   RO           10    i.SVC_Handler       gd32f4xx_it.o
    0x08004554   0x08004554   0x00000090   Code   RO          575    i.SampleProc        adc_app.o
    0x080045e4   0x080045e4   0x0000003c   Code   RO          576    i.SampleTask        adc_app.o
    0x08004620   0x08004620   0x0000008c   Code   RO          318    i.SetRtc            gd32f470vet6_bsp.o
    0x080046ac   0x080046ac   0x00000030   Code   RO          319    i.SysInit           gd32f470vet6_bsp.o
    0x080046dc   0x080046dc   0x00000018   Code   RO           11    i.SysTick_Handler   gd32f4xx_it.o
    0x080046f4   0x080046f4   0x0000017c   Code   RO         1977    i.SystemInit        system_gd32f4xx.o
    0x08004870   0x08004870   0x00000108   Code   RO          795    i.TF_WriteConfig    tf_app.o
    0x08004978   0x08004978   0x00000198   Code   RO          796    i.TF_WriteLimit     tf_app.o
    0x08004b10   0x08004b10   0x000000c8   Code   RO          797    i.TF_WriteRatio     tf_app.o
    0x08004bd8   0x08004bd8   0x0000005c   Code   RO          507    i.TaskExeution      scheduler.o
    0x08004c34   0x08004c34   0x00000088   Code   RO          320    i.TfPeriphInit      gd32f470vet6_bsp.o
    0x08004cbc   0x08004cbc   0x000000a4   Code   RO           12    i.USART0_IRQHandler  gd32f4xx_it.o
    0x08004d60   0x08004d60   0x000000a4   Code   RO           13    i.USART1_IRQHandler  gd32f4xx_it.o
    0x08004e04   0x08004e04   0x00000004   Code   RO           14    i.UsageFault_Handler  gd32f4xx_it.o
    0x08004e08   0x08004e08   0x0000006c   Code   RO          321    i.Usart0DmaInit     gd32f470vet6_bsp.o
    0x08004e74   0x08004e74   0x000000b4   Code   RO          322    i.Usart0PeriphInit  gd32f470vet6_bsp.o
    0x08004f28   0x08004f28   0x00000070   Code   RO          323    i.Usart0Printf      gd32f470vet6_bsp.o
    0x08004f98   0x08004f98   0x00000038   Code   RO          851    i.Usart0Task        usart_app.o
    0x08004fd0   0x08004fd0   0x0000006c   Code   RO          324    i.Usart1DmaInit     gd32f470vet6_bsp.o
    0x0800503c   0x0800503c   0x000000c4   Code   RO          325    i.Usart1PeriphInit  gd32f470vet6_bsp.o
    0x08005100   0x08005100   0x00000084   Code   RO          326    i.Usart1Printf      gd32f470vet6_bsp.o
    0x08005184   0x08005184   0x0000002c   Code   RO          852    i.Usart1Task        usart_app.o
    0x080051b0   0x080051b0   0x00000060   Code   RO          327    i.ValidateRtcTime   gd32f470vet6_bsp.o
    0x08005210   0x08005210   0x00000030   Code   RO         7756    i.__ARM_fpclassify  m_wm.l(fpclassify.o)
    0x08005240   0x08005240   0x000000f8   Code   RO         7758    i.__hardfp___mathlib_tofloat  m_wm.l(narrow.o)
    0x08005338   0x08005338   0x000000d0   Code   RO         7865    i.__hardfp_ldexp    m_wm.l(ldexp.o)
    0x08005408   0x08005408   0x00000020   Code   RO         7898    i.__mathlib_dbl_overflow  m_wm.l(dunder.o)
    0x08005428   0x08005428   0x00000020   Code   RO         7900    i.__mathlib_dbl_underflow  m_wm.l(dunder.o)
    0x08005448   0x08005448   0x00000012   Code   RO         7759    i.__mathlib_narrow  m_wm.l(narrow.o)
    0x0800545a   0x0800545a   0x00000014   Code   RO         7867    i.__support_ldexp   m_wm.l(ldexp.o)
    0x0800546e   0x0800546e   0x0000000e   Code   RO         7534    i._is_digit         c_w.l(__printf_wp.o)
    0x0800547c   0x0800547c   0x0000001c   Code   RO         1978    i._soft_delay_      system_gd32f4xx.o
    0x08005498   0x08005498   0x00000048   Code   RO         1893    i.ad3344_init       gd30ad3344.o
    0x080054e0   0x080054e0   0x0000005c   Code   RO         1894    i.ad3344_read_data16  gd30ad3344.o
    0x0800553c   0x0800553c   0x0000000c   Code   RO         1897    i.ad3344_reset      gd30ad3344.o
    0x08005548   0x08005548   0x0000006c   Code   RO         1898    i.ad3344_spi_txrx16bit  gd30ad3344.o
    0x080055b4   0x080055b4   0x0000002a   Code   RO         2036    i.adc_calibration_enable  gd32f4xx_adc.o
    0x080055de   0x080055de   0x00000052   Code   RO         2038    i.adc_channel_length_config  gd32f4xx_adc.o
    0x08005630   0x08005630   0x00000024   Code   RO         2039    i.adc_clock_config  gd32f4xx_adc.o
    0x08005654   0x08005654   0x00000016   Code   RO         2040    i.adc_data_alignment_config  gd32f4xx_adc.o
    0x0800566a   0x0800566a   0x0000000a   Code   RO         2045    i.adc_dma_mode_enable  gd32f4xx_adc.o
    0x08005674   0x08005674   0x0000000a   Code   RO         2047    i.adc_dma_request_after_last_enable  gd32f4xx_adc.o
    0x0800567e   0x0800567e   0x00000012   Code   RO         2048    i.adc_enable        gd32f4xx_adc.o
    0x08005690   0x08005690   0x00000034   Code   RO         2050    i.adc_external_trigger_config  gd32f4xx_adc.o
    0x080056c4   0x080056c4   0x00000030   Code   RO         2051    i.adc_external_trigger_source_config  gd32f4xx_adc.o
    0x080056f4   0x080056f4   0x000000ac   Code   RO         2066    i.adc_routine_channel_config  gd32f4xx_adc.o
    0x080057a0   0x080057a0   0x00000024   Code   RO         2069    i.adc_software_trigger_enable  gd32f4xx_adc.o
    0x080057c4   0x080057c4   0x0000005a   Code   RO         2070    i.adc_special_function_config  gd32f4xx_adc.o
    0x0800581e   0x0800581e   0x00000002   PAD
    0x08005820   0x08005820   0x00000024   Code   RO         2075    i.adc_sync_mode_config  gd32f4xx_adc.o
    0x08005844   0x08005844   0x00000026   Code   RO          875    i.bit_array_and     ebtn.o
    0x0800586a   0x0800586a   0x0000002e   Code   RO          876    i.bit_array_assign  ebtn.o
    0x08005898   0x08005898   0x00000024   Code   RO          877    i.bit_array_cmp     ebtn.o
    0x080058bc   0x080058bc   0x00000016   Code   RO          878    i.bit_array_get     ebtn.o
    0x080058d2   0x080058d2   0x00000054   Code   RO          879    i.bit_array_num_bits_set  ebtn.o
    0x08005926   0x08005926   0x00000026   Code   RO          880    i.bit_array_or      ebtn.o
    0x0800594c   0x0800594c   0x00000090   Code   RO         1587    i.check_fs          ff.o
    0x080059dc   0x080059dc   0x00000014   Code   RO         1588    i.chk_chr           ff.o
    0x080059f0   0x080059f0   0x00000394   Code   RO         1589    i.chk_mounted       ff.o
    0x08005d84   0x08005d84   0x0000001a   Code   RO         1590    i.clust2sect        ff.o
    0x08005d9e   0x08005d9e   0x00000002   PAD
    0x08005da0   0x08005da0   0x00000030   Code   RO         1316    i.cmdsent_error_check  sdio_sdcard.o
    0x08005dd0   0x08005dd0   0x00000090   Code   RO         1591    i.cmp_lfn           ff.o
    0x08005e60   0x08005e60   0x000000ca   Code   RO         1592    i.create_chain      ff.o
    0x08005f2a   0x08005f2a   0x00000002   PAD
    0x08005f2c   0x08005f2c   0x00000270   Code   RO         1593    i.create_name       ff.o
    0x0800619c   0x0800619c   0x00000014   Code   RO          528    i.delay_1ms         systick.o
    0x080061b0   0x080061b0   0x00000018   Code   RO          529    i.delay_decrement   systick.o
    0x080061c8   0x080061c8   0x0000001a   Code   RO         1900    i.delay_us          gd30ad3344.o
    0x080061e2   0x080061e2   0x000000de   Code   RO         1594    i.dir_find          ff.o
    0x080062c0   0x080062c0   0x00000118   Code   RO         1595    i.dir_next          ff.o
    0x080063d8   0x080063d8   0x0000018c   Code   RO         1597    i.dir_register      ff.o
    0x08006564   0x08006564   0x0000009c   Code   RO         1599    i.dir_sdi           ff.o
    0x08006600   0x08006600   0x00000086   Code   RO         1535    i.disk_initialize   diskio.o
    0x08006686   0x08006686   0x00000006   Code   RO         1536    i.disk_ioctl        diskio.o
    0x0800668c   0x0800668c   0x00000050   Code   RO         1537    i.disk_read         diskio.o
    0x080066dc   0x080066dc   0x0000000c   Code   RO         1538    i.disk_status       diskio.o
    0x080066e8   0x080066e8   0x00000050   Code   RO         1539    i.disk_write        diskio.o
    0x08006738   0x08006738   0x00000020   Code   RO         3076    i.dma_channel_disable  gd32f4xx_dma.o
    0x08006758   0x08006758   0x00000020   Code   RO         3077    i.dma_channel_enable  gd32f4xx_dma.o
    0x08006778   0x08006778   0x00000026   Code   RO         3078    i.dma_channel_subperipheral_select  gd32f4xx_dma.o
    0x0800679e   0x0800679e   0x00000020   Code   RO         3079    i.dma_circulation_disable  gd32f4xx_dma.o
    0x080067be   0x080067be   0x00000020   Code   RO         3080    i.dma_circulation_enable  gd32f4xx_dma.o
    0x080067de   0x080067de   0x000000a6   Code   RO         3081    i.dma_deinit        gd32f4xx_dma.o
    0x08006884   0x08006884   0x0000003e   Code   RO         3083    i.dma_flag_clear    gd32f4xx_dma.o
    0x080068c2   0x080068c2   0x0000004c   Code   RO         3084    i.dma_flag_get      gd32f4xx_dma.o
    0x0800690e   0x0800690e   0x00000040   Code   RO         3085    i.dma_flow_controller_config  gd32f4xx_dma.o
    0x0800694e   0x0800694e   0x00000020   Code   RO         3090    i.dma_memory_address_config  gd32f4xx_dma.o
    0x0800696e   0x0800696e   0x00000002   PAD
    0x08006970   0x08006970   0x00000164   Code   RO         3094    i.dma_multi_data_mode_init  gd32f4xx_dma.o
    0x08006ad4   0x08006ad4   0x000000b4   Code   RO         1317    i.dma_receive_config  sdio_sdcard.o
    0x08006b88   0x08006b88   0x00000158   Code   RO         3101    i.dma_single_data_mode_init  gd32f4xx_dma.o
    0x08006ce0   0x08006ce0   0x00000022   Code   RO         3102    i.dma_single_data_para_struct_init  gd32f4xx_dma.o
    0x08006d02   0x08006d02   0x00000002   PAD
    0x08006d04   0x08006d04   0x000000b4   Code   RO         1318    i.dma_transfer_config  sdio_sdcard.o
    0x08006db8   0x08006db8   0x00000010   Code   RO         3106    i.dma_transfer_number_config  gd32f4xx_dma.o
    0x08006dc8   0x08006dc8   0x00000010   Code   RO         3107    i.dma_transfer_number_get  gd32f4xx_dma.o
    0x08006dd8   0x08006dd8   0x00000020   Code   RO          881    i.ebtn_combo_btn_add_btn  ebtn.o
    0x08006df8   0x08006df8   0x00000020   Code   RO          882    i.ebtn_combo_btn_add_btn_by_idx  ebtn.o
    0x08006e18   0x08006e18   0x00000048   Code   RO          889    i.ebtn_get_btn_index_by_key_id  ebtn.o
    0x08006e60   0x08006e60   0x00000058   Code   RO          891    i.ebtn_get_current_state  ebtn.o
    0x08006eb8   0x08006eb8   0x0000005c   Code   RO          893    i.ebtn_init         ebtn.o
    0x08006f14   0x08006f14   0x0000001a   Code   RO          897    i.ebtn_process      ebtn.o
    0x08006f2e   0x08006f2e   0x0000003e   Code   RO          898    i.ebtn_process_btn  ebtn.o
    0x08006f6c   0x08006f6c   0x0000007c   Code   RO          899    i.ebtn_process_btn_combo  ebtn.o
    0x08006fe8   0x08006fe8   0x000001c0   Code   RO          900    i.ebtn_process_with_curr_state  ebtn.o
    0x080071a8   0x080071a8   0x0000000c   Code   RO          902    i.ebtn_set_combo_suppress_threshold  ebtn.o
    0x080071b4   0x080071b4   0x0000000c   Code   RO          903    i.ebtn_set_config   ebtn.o
    0x080071c0   0x080071c0   0x00000006   Code   RO          904    i.ebtn_timer_sub    ebtn.o
    0x080071c6   0x080071c6   0x00000016   Code   RO         1601    i.f_close           ff.o
    0x080071dc   0x080071dc   0x0000002c   Code   RO         1606    i.f_mount           ff.o
    0x08007208   0x08007208   0x00000174   Code   RO         1607    i.f_open            ff.o
    0x0800737c   0x0800737c   0x000001ce   Code   RO         1612    i.f_read            ff.o
    0x0800754a   0x0800754a   0x000000b8   Code   RO         1616    i.f_sync            ff.o
    0x08007602   0x08007602   0x0000020e   Code   RO         1620    i.f_write           ff.o
    0x08007810   0x08007810   0x00000014   Code   RO         1869    i.ff_convert        unicode.o
    0x08007824   0x08007824   0x00000044   Code   RO         1870    i.ff_wtoupper       unicode.o
    0x08007868   0x08007868   0x00000080   Code   RO         1621    i.fit_lfn           ff.o
    0x080078e8   0x080078e8   0x0000009e   Code   RO         1622    i.follow_path       ff.o
    0x08007986   0x08007986   0x00000002   PAD
    0x08007988   0x08007988   0x0000008c   Code   RO         7840    i.frexp             m_wm.l(frexp.o)
    0x08007a14   0x08007a14   0x000000be   Code   RO         1623    i.gen_numname       ff.o
    0x08007ad2   0x08007ad2   0x000000e4   Code   RO         1624    i.get_fat           ff.o
    0x08007bb6   0x08007bb6   0x00000004   Code   RO         1540    i.get_fattime       diskio.o
    0x08007bba   0x08007bba   0x0000005e   Code   RO         4495    i.gpio_af_set       gd32f4xx_gpio.o
    0x08007c18   0x08007c18   0x00000004   Code   RO         4496    i.gpio_bit_reset    gd32f4xx_gpio.o
    0x08007c1c   0x08007c1c   0x00000004   Code   RO         4497    i.gpio_bit_set      gd32f4xx_gpio.o
    0x08007c20   0x08007c20   0x0000000a   Code   RO         4499    i.gpio_bit_write    gd32f4xx_gpio.o
    0x08007c2a   0x08007c2a   0x00000002   PAD
    0x08007c2c   0x08007c2c   0x00000074   Code   RO         1319    i.gpio_config       sdio_sdcard.o
    0x08007ca0   0x08007ca0   0x00000010   Code   RO         4501    i.gpio_input_bit_get  gd32f4xx_gpio.o
    0x08007cb0   0x08007cb0   0x0000004e   Code   RO         4503    i.gpio_mode_set     gd32f4xx_gpio.o
    0x08007cfe   0x08007cfe   0x00000042   Code   RO         4505    i.gpio_output_options_set  gd32f4xx_gpio.o
    0x08007d40   0x08007d40   0x00000010   Code   RO         4599    i.i2c_ack_config    gd32f4xx_i2c.o
    0x08007d50   0x08007d50   0x000000e4   Code   RO         4603    i.i2c_clock_config  gd32f4xx_i2c.o
    0x08007e34   0x08007e34   0x00000058   Code   RO         4606    i.i2c_deinit        gd32f4xx_i2c.o
    0x08007e8c   0x08007e8c   0x00000010   Code   RO         4609    i.i2c_dma_config    gd32f4xx_i2c.o
    0x08007e9c   0x08007e9c   0x0000000a   Code   RO         4613    i.i2c_enable        gd32f4xx_i2c.o
    0x08007ea6   0x08007ea6   0x00000028   Code   RO         4614    i.i2c_flag_clear    gd32f4xx_i2c.o
    0x08007ece   0x08007ece   0x0000001e   Code   RO         4615    i.i2c_flag_get      gd32f4xx_i2c.o
    0x08007eec   0x08007eec   0x00000014   Code   RO         4620    i.i2c_master_addressing  gd32f4xx_i2c.o
    0x08007f00   0x08007f00   0x0000001c   Code   RO         4621    i.i2c_mode_addr_config  gd32f4xx_i2c.o
    0x08007f1c   0x08007f1c   0x0000000a   Code   RO         4634    i.i2c_start_on_bus  gd32f4xx_i2c.o
    0x08007f26   0x08007f26   0x0000000a   Code   RO         4635    i.i2c_stop_on_bus   gd32f4xx_i2c.o
    0x08007f30   0x08007f30   0x00000010   Code   RO          479    i.main              main.o
    0x08007f40   0x08007f40   0x00000026   Code   RO         1626    i.mem_cmp           ff.o
    0x08007f66   0x08007f66   0x0000001a   Code   RO         1627    i.mem_cpy           ff.o
    0x08007f80   0x08007f80   0x00000014   Code   RO         1628    i.mem_set           ff.o
    0x08007f94   0x08007f94   0x00000072   Code   RO         1629    i.move_window       ff.o
    0x08008006   0x08008006   0x00000002   PAD
    0x08008008   0x08008008   0x000000c4   Code   RO         5068    i.nvic_irq_enable   gd32f4xx_misc.o
    0x080080cc   0x080080cc   0x00000014   Code   RO         5069    i.nvic_priority_group_set  gd32f4xx_misc.o
    0x080080e0   0x080080e0   0x00000014   Code   RO         5125    i.pmu_backup_write_enable  gd32f4xx_pmu.o
    0x080080f4   0x080080f4   0x0000004c   Code   RO          905    i.prv_get_combo_btn_by_key_id  ebtn.o
    0x08008140   0x08008140   0x00000374   Code   RO          906    i.prv_process_btn   ebtn.o
    0x080084b4   0x080084b4   0x00000136   Code   RO         1631    i.put_fat           ff.o
    0x080085ea   0x080085ea   0x00000002   PAD
    0x080085ec   0x080085ec   0x00000084   Code   RO         1320    i.r1_error_check    sdio_sdcard.o
    0x08008670   0x08008670   0x000000ae   Code   RO         1321    i.r1_error_type_check  sdio_sdcard.o
    0x0800871e   0x0800871e   0x00000002   PAD
    0x08008720   0x08008720   0x00000050   Code   RO         1322    i.r2_error_check    sdio_sdcard.o
    0x08008770   0x08008770   0x0000003c   Code   RO         1323    i.r3_error_check    sdio_sdcard.o
    0x080087ac   0x080087ac   0x000000a8   Code   RO         1324    i.r6_error_check    sdio_sdcard.o
    0x08008854   0x08008854   0x00000050   Code   RO         1325    i.r7_error_check    sdio_sdcard.o
    0x080088a4   0x080088a4   0x00000014   Code   RO         5265    i.rcu_all_reset_flag_clear  gd32f4xx_rcu.o
    0x080088b8   0x080088b8   0x00000124   Code   RO         5273    i.rcu_clock_freq_get  gd32f4xx_rcu.o
    0x080089dc   0x080089dc   0x00000024   Code   RO         1326    i.rcu_config        sdio_sdcard.o
    0x08008a00   0x08008a00   0x00000024   Code   RO         5276    i.rcu_flag_get      gd32f4xx_rcu.o
    0x08008a24   0x08008a24   0x00000024   Code   RO         5289    i.rcu_osci_on       gd32f4xx_rcu.o
    0x08008a48   0x08008a48   0x0000015c   Code   RO         5290    i.rcu_osci_stab_wait  gd32f4xx_rcu.o
    0x08008ba4   0x08008ba4   0x00000024   Code   RO         5292    i.rcu_periph_clock_enable  gd32f4xx_rcu.o
    0x08008bc8   0x08008bc8   0x00000024   Code   RO         5295    i.rcu_periph_reset_disable  gd32f4xx_rcu.o
    0x08008bec   0x08008bec   0x00000024   Code   RO         5296    i.rcu_periph_reset_enable  gd32f4xx_rcu.o
    0x08008c10   0x08008c10   0x00000018   Code   RO         5301    i.rcu_rtc_clock_config  gd32f4xx_rcu.o
    0x08008c28   0x08008c28   0x00000068   Code   RO         1632    i.remove_chain      ff.o
    0x08008c90   0x08008c90   0x00000064   Code   RO         5573    i.rtc_current_time_get  gd32f4xx_rtc.o
    0x08008cf4   0x08008cf4   0x000000c4   Code   RO         5578    i.rtc_init          gd32f4xx_rtc.o
    0x08008db8   0x08008db8   0x00000048   Code   RO         5579    i.rtc_init_mode_enter  gd32f4xx_rtc.o
    0x08008e00   0x08008e00   0x00000014   Code   RO         5580    i.rtc_init_mode_exit  gd32f4xx_rtc.o
    0x08008e14   0x08008e14   0x00000060   Code   RO         5585    i.rtc_register_sync_wait  gd32f4xx_rtc.o
    0x08008e74   0x08008e74   0x00000218   Code   RO         1327    i.sd_block_read     sdio_sdcard.o
    0x0800908c   0x0800908c   0x00000320   Code   RO         1328    i.sd_block_write    sdio_sdcard.o
    0x080093ac   0x080093ac   0x00000094   Code   RO         1329    i.sd_bus_mode_config  sdio_sdcard.o
    0x08009440   0x08009440   0x000000fc   Code   RO         1330    i.sd_bus_width_config  sdio_sdcard.o
    0x0800953c   0x0800953c   0x000002c0   Code   RO         1332    i.sd_card_information_get  sdio_sdcard.o
    0x080097fc   0x080097fc   0x0000011c   Code   RO         1333    i.sd_card_init      sdio_sdcard.o
    0x08009918   0x08009918   0x00000026   Code   RO         1334    i.sd_card_select_deselect  sdio_sdcard.o
    0x0800993e   0x0800993e   0x00000002   PAD
    0x08009940   0x08009940   0x000000b8   Code   RO         1335    i.sd_card_state_get  sdio_sdcard.o
    0x080099f8   0x080099f8   0x00000048   Code   RO         1336    i.sd_cardstatus_get  sdio_sdcard.o
    0x08009a40   0x08009a40   0x00000018   Code   RO         1337    i.sd_datablocksize_get  sdio_sdcard.o
    0x08009a58   0x08009a58   0x00000046   Code   RO         1339    i.sd_init           sdio_sdcard.o
    0x08009a9e   0x08009a9e   0x00000002   PAD
    0x08009aa0   0x08009aa0   0x00000130   Code   RO         1340    i.sd_interrupts_process  sdio_sdcard.o
    0x08009bd0   0x08009bd0   0x0000029c   Code   RO         1342    i.sd_multiblocks_read  sdio_sdcard.o
    0x08009e6c   0x08009e6c   0x00000398   Code   RO         1343    i.sd_multiblocks_write  sdio_sdcard.o
    0x0800a204   0x0800a204   0x0000012c   Code   RO         1345    i.sd_power_on       sdio_sdcard.o
    0x0800a330   0x0800a330   0x0000015c   Code   RO         1346    i.sd_scr_get        sdio_sdcard.o
    0x0800a48c   0x0800a48c   0x00000018   Code   RO         1348    i.sd_transfer_mode_config  sdio_sdcard.o
    0x0800a4a4   0x0800a4a4   0x00000024   Code   RO         1350    i.sd_transfer_stop  sdio_sdcard.o
    0x0800a4c8   0x0800a4c8   0x0000001c   Code   RO         5826    i.sdio_bus_mode_set  gd32f4xx_sdio.o
    0x0800a4e4   0x0800a4e4   0x00000034   Code   RO         5833    i.sdio_clock_config  gd32f4xx_sdio.o
    0x0800a518   0x0800a518   0x00000014   Code   RO         5835    i.sdio_clock_enable  gd32f4xx_sdio.o
    0x0800a52c   0x0800a52c   0x0000000c   Code   RO         5836    i.sdio_command_index_get  gd32f4xx_sdio.o
    0x0800a538   0x0800a538   0x00000038   Code   RO         5837    i.sdio_command_response_config  gd32f4xx_sdio.o
    0x0800a570   0x0800a570   0x00000014   Code   RO         5839    i.sdio_csm_enable   gd32f4xx_sdio.o
    0x0800a584   0x0800a584   0x0000003c   Code   RO         5840    i.sdio_data_config  gd32f4xx_sdio.o
    0x0800a5c0   0x0800a5c0   0x0000000c   Code   RO         5842    i.sdio_data_read    gd32f4xx_sdio.o
    0x0800a5cc   0x0800a5cc   0x0000001c   Code   RO         5843    i.sdio_data_transfer_config  gd32f4xx_sdio.o
    0x0800a5e8   0x0800a5e8   0x0000000c   Code   RO         5844    i.sdio_data_write   gd32f4xx_sdio.o
    0x0800a5f4   0x0800a5f4   0x00000014   Code   RO         5845    i.sdio_deinit       gd32f4xx_sdio.o
    0x0800a608   0x0800a608   0x00000014   Code   RO         5846    i.sdio_dma_disable  gd32f4xx_sdio.o
    0x0800a61c   0x0800a61c   0x00000014   Code   RO         5847    i.sdio_dma_enable   gd32f4xx_sdio.o
    0x0800a630   0x0800a630   0x00000014   Code   RO         5848    i.sdio_dsm_disable  gd32f4xx_sdio.o
    0x0800a644   0x0800a644   0x00000014   Code   RO         5849    i.sdio_dsm_enable   gd32f4xx_sdio.o
    0x0800a658   0x0800a658   0x0000000c   Code   RO         5851    i.sdio_flag_clear   gd32f4xx_sdio.o
    0x0800a664   0x0800a664   0x00000014   Code   RO         5852    i.sdio_flag_get     gd32f4xx_sdio.o
    0x0800a678   0x0800a678   0x00000014   Code   RO         5853    i.sdio_hardware_clock_disable  gd32f4xx_sdio.o
    0x0800a68c   0x0800a68c   0x00000010   Code   RO         5855    i.sdio_interrupt_disable  gd32f4xx_sdio.o
    0x0800a69c   0x0800a69c   0x00000010   Code   RO         5856    i.sdio_interrupt_enable  gd32f4xx_sdio.o
    0x0800a6ac   0x0800a6ac   0x0000000c   Code   RO         5857    i.sdio_interrupt_flag_clear  gd32f4xx_sdio.o
    0x0800a6b8   0x0800a6b8   0x00000014   Code   RO         5858    i.sdio_interrupt_flag_get  gd32f4xx_sdio.o
    0x0800a6cc   0x0800a6cc   0x0000000c   Code   RO         5861    i.sdio_power_state_get  gd32f4xx_sdio.o
    0x0800a6d8   0x0800a6d8   0x0000000c   Code   RO         5862    i.sdio_power_state_set  gd32f4xx_sdio.o
    0x0800a6e4   0x0800a6e4   0x0000003c   Code   RO         5866    i.sdio_response_get  gd32f4xx_sdio.o
    0x0800a720   0x0800a720   0x0000001c   Code   RO         5871    i.sdio_wait_type_set  gd32f4xx_sdio.o
    0x0800a73c   0x0800a73c   0x00000016   Code   RO         6130    i.spi_dma_disable   gd32f4xx_spi.o
    0x0800a752   0x0800a752   0x00000016   Code   RO         6131    i.spi_dma_enable    gd32f4xx_spi.o
    0x0800a768   0x0800a768   0x0000000a   Code   RO         6132    i.spi_enable        gd32f4xx_spi.o
    0x0800a772   0x0800a772   0x00000002   PAD
    0x0800a774   0x0800a774   0x00000054   Code   RO         1217    i.spi_flash_buffer_read  gd25qxx.o
    0x0800a7c8   0x0800a7c8   0x0000001c   Code   RO         1220    i.spi_flash_init    gd25qxx.o
    0x0800a7e4   0x0800a7e4   0x000000fc   Code   RO         1224    i.spi_flash_send_byte_dma  gd25qxx.o
    0x0800a8e0   0x0800a8e0   0x00000008   Code   RO         6134    i.spi_i2s_data_receive  gd32f4xx_spi.o
    0x0800a8e8   0x0800a8e8   0x00000004   Code   RO         6135    i.spi_i2s_data_transmit  gd32f4xx_spi.o
    0x0800a8ec   0x0800a8ec   0x00000010   Code   RO         6137    i.spi_i2s_flag_get  gd32f4xx_spi.o
    0x0800a8fc   0x0800a8fc   0x00000032   Code   RO         6142    i.spi_init          gd32f4xx_spi.o
    0x0800a92e   0x0800a92e   0x00000020   Code   RO         1633    i.sum_sfn           ff.o
    0x0800a94e   0x0800a94e   0x000000ca   Code   RO         1634    i.sync              ff.o
    0x0800aa18   0x0800aa18   0x00000110   Code   RO         1980    i.system_clock_240m_25m_hxtal  system_gd32f4xx.o
    0x0800ab28   0x0800ab28   0x00000008   Code   RO         1981    i.system_clock_config  system_gd32f4xx.o
    0x0800ab30   0x0800ab30   0x00000050   Code   RO          530    i.systick_config    systick.o
    0x0800ab80   0x0800ab80   0x000000e8   Code   RO         7117    i.usart_baudrate_set  gd32f4xx_usart.o
    0x0800ac68   0x0800ac68   0x0000000a   Code   RO         7121    i.usart_data_receive  gd32f4xx_usart.o
    0x0800ac72   0x0800ac72   0x00000008   Code   RO         7122    i.usart_data_transmit  gd32f4xx_usart.o
    0x0800ac7a   0x0800ac7a   0x00000002   PAD
    0x0800ac7c   0x0800ac7c   0x000000dc   Code   RO         7123    i.usart_deinit      gd32f4xx_usart.o
    0x0800ad58   0x0800ad58   0x00000014   Code   RO         7125    i.usart_dma_receive_config  gd32f4xx_usart.o
    0x0800ad6c   0x0800ad6c   0x0000000a   Code   RO         7127    i.usart_enable      gd32f4xx_usart.o
    0x0800ad76   0x0800ad76   0x0000001e   Code   RO         7129    i.usart_flag_get    gd32f4xx_usart.o
    0x0800ad94   0x0800ad94   0x0000001a   Code   RO         7136    i.usart_interrupt_disable  gd32f4xx_usart.o
    0x0800adae   0x0800adae   0x0000001a   Code   RO         7137    i.usart_interrupt_enable  gd32f4xx_usart.o
    0x0800adc8   0x0800adc8   0x00000038   Code   RO         7139    i.usart_interrupt_flag_get  gd32f4xx_usart.o
    0x0800ae00   0x0800ae00   0x00000010   Code   RO         7154    i.usart_receive_config  gd32f4xx_usart.o
    0x0800ae10   0x0800ae10   0x00000010   Code   RO         7169    i.usart_transmit_config  gd32f4xx_usart.o
    0x0800ae20   0x0800ae20   0x0000002a   Code   RO         1635    i.validate          ff.o
    0x0800ae4a   0x0800ae4a   0x00000002   PAD
    0x0800ae4c   0x0800ae4c   0x0000002c   Code   RO         7742    locale$$code        c_w.l(lc_numeric_c.o)
    0x0800ae78   0x0800ae78   0x0000002c   Code   RO         7849    locale$$code        c_w.l(lc_ctype_c.o)
    0x0800aea4   0x0800aea4   0x00000062   Code   RO         7832    x$fpl$d2f           fz_wm.l(d2f.o)
    0x0800af06   0x0800af06   0x00000002   PAD
    0x0800af08   0x0800af08   0x00000010   Code   RO         7928    x$fpl$dcheck1       fz_wm.l(dcheck1.o)
    0x0800af18   0x0800af18   0x00000018   Code   RO         7853    x$fpl$dcmpinf       fz_wm.l(dcmpi.o)
    0x0800af30   0x0800af30   0x00000078   Code   RO         7834    x$fpl$deqf          fz_wm.l(deqf.o)
    0x0800afa8   0x0800afa8   0x00000078   Code   RO         7855    x$fpl$dleqf         fz_wm.l(dleqf.o)
    0x0800b020   0x0800b020   0x00000154   Code   RO         7857    x$fpl$dmul          fz_wm.l(dmul.o)
    0x0800b174   0x0800b174   0x0000009c   Code   RO         7859    x$fpl$dnaninf       fz_wm.l(dnaninf.o)
    0x0800b210   0x0800b210   0x0000000c   Code   RO         7659    x$fpl$dretinf       fz_wm.l(dretinf.o)
    0x0800b21c   0x0800b21c   0x0000006c   Code   RO         7836    x$fpl$drleqf        fz_wm.l(drleqf.o)
    0x0800b288   0x0800b288   0x00000056   Code   RO         7572    x$fpl$f2d           fz_wm.l(f2d.o)
    0x0800b2de   0x0800b2de   0x0000008c   Code   RO         7661    x$fpl$fnaninf       fz_wm.l(fnaninf.o)
    0x0800b36a   0x0800b36a   0x0000000a   Code   RO         7861    x$fpl$fpinit        fz_wm.l(fpinit.o)
    0x0800b374   0x0800b374   0x0000000a   Code   RO         7863    x$fpl$fretinf       fz_wm.l(fretinf.o)
    0x0800b37e   0x0800b37e   0x00000006   Code   RO         7748    x$fpl$ieeestatus    fz_wm.l(istatus.o)
    0x0800b384   0x0800b384   0x00000004   Code   RO         7574    x$fpl$printf1       fz_wm.l(printf1.o)
    0x0800b388   0x0800b388   0x00000004   Code   RO         7663    x$fpl$printf2       fz_wm.l(printf2.o)
    0x0800b38c   0x0800b38c   0x00000064   Code   RO         7961    x$fpl$retnan        fz_wm.l(retnan.o)
    0x0800b3f0   0x0800b3f0   0x0000005c   Code   RO         7892    x$fpl$scalbn        fz_wm.l(scalbn.o)
    0x0800b44c   0x0800b44c   0x00000004   Code   RO         7576    x$fpl$scanf1        fz_wm.l(scanf1.o)
    0x0800b450   0x0800b450   0x00000008   Code   RO         7750    x$fpl$scanf2        fz_wm.l(scanf2.o)
    0x0800b458   0x0800b458   0x00000030   Code   RO         7967    x$fpl$trapveneer    fz_wm.l(trapv.o)
    0x0800b488   0x0800b488   0x00000000   Code   RO         7669    x$fpl$usenofp       fz_wm.l(usenofp.o)
    0x0800b488   0x0800b488   0x00000040   Data   RO          329    .constdata          gd32f470vet6_bsp.o
    0x0800b4c8   0x0800b4c8   0x00000018   Data   RO          802    .constdata          tf_app.o
    0x0800b4e0   0x0800b4e0   0x00000a98   Data   RO         1094    .constdata          oled.o
    0x0800bf78   0x0800bf78   0x0000000d   Data   RO         1636    .constdata          ff.o
    0x0800bf85   0x0800bf85   0x00000001   PAD
    0x0800bf86   0x0800bf86   0x00000178   Data   RO         1871    .constdata          unicode.o
    0x0800c0fe   0x0800c0fe   0x00000011   Data   RO         7542    .constdata          c_w.l(__printf_flags_ss_wp.o)
    0x0800c10f   0x0800c10f   0x00000001   PAD
    0x0800c110   0x0800c110   0x00000008   Data   RO         7598    .constdata          c_w.l(_printf_wctomb.o)
    0x0800c118   0x0800c118   0x00000028   Data   RO         7627    .constdata          c_w.l(_printf_hex_int_ll_ptr.o)
    0x0800c140   0x0800c140   0x00000026   Data   RO         7702    .constdata          c_w.l(_printf_fp_hex.o)
    0x0800c166   0x0800c166   0x00000002   PAD
    0x0800c168   0x0800c168   0x00000094   Data   RO         7715    .constdata          c_w.l(bigflt0.o)
    0x0800c1fc   0x0800c1fc   0x0000001d   Data   RO          481    .conststring        main.o
    0x0800c219   0x0800c219   0x00000003   PAD
    0x0800c21c   0x0800c21c   0x0000004e   Data   RO          577    .conststring        adc_app.o
    0x0800c26a   0x0800c26a   0x00000002   PAD
    0x0800c26c   0x0800c26c   0x000000ad   Data   RO          803    .conststring        tf_app.o
    0x0800c319   0x0800c319   0x00000003   PAD
    0x0800c31c   0x0800c31c   0x00000020   Data   RO         7989    Region$$Table       anon$$obj.o
    0x0800c33c   0x0800c33c   0x00000008   Data   RO         7744    c$$dinf             fz_wm.l(fpconst.o)
    0x0800c344   0x0800c344   0x00000008   Data   RO         7747    c$$dmax             fz_wm.l(fpconst.o)
    0x0800c34c   0x0800c34c   0x0000001c   Data   RO         7741    locale$$data        c_w.l(lc_numeric_c.o)
    0x0800c368   0x0800c368   0x00000110   Data   RO         7848    locale$$data        c_w.l(lc_ctype_c.o)


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x0800c478, Size: 0x000036d0, Max: 0x00030000, ABSOLUTE, COMPRESSED[0x000000bc])

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   COMPRESSED   0x00000285   Data   RW          330    .data               gd32f470vet6_bsp.o
    0x20000285   COMPRESSED   0x00000003   PAD
    0x20000288   COMPRESSED   0x0000005c   Data   RW          482    .data               main.o
    0x200002e4   COMPRESSED   0x00000004   Data   RW          531    .data               systick.o
    0x200002e8   COMPRESSED   0x00000008   Data   RW          578    .data               adc_app.o
    0x200002f0   COMPRESSED   0x0000001a   Data   RW         1095    .data               oled.o
    0x2000030a   COMPRESSED   0x00000002   PAD
    0x2000030c   COMPRESSED   0x00000024   Data   RW         1352    .data               sdio_sdcard.o
    0x20000330   COMPRESSED   0x00000006   Data   RW         1637    .data               ff.o
    0x20000336   COMPRESSED   0x00000006   Data   RW         1901    .data               gd30ad3344.o
    0x2000033c   COMPRESSED   0x00000004   Data   RW         1982    .data               system_gd32f4xx.o
    0x20000340        -       0x000012b4   Zero   RW          328    .bss                gd32f470vet6_bsp.o
    0x200015f4        -       0x00000020   Zero   RW          480    .bss                main.o
    0x20001614        -       0x0000003c   Zero   RW          907    .bss                ebtn.o
    0x20001650        -       0x00000020   Zero   RW         1351    .bss                sdio_sdcard.o
    0x20001670        -       0x00000060   Zero   RW         7767    .bss                c_w.l(libspace.o)
    0x200016d0        -       0x00001000   Zero   RW         2028    HEAP                startup_gd32f450_470.o
    0x200026d0        -       0x00001000   Zero   RW         2027    STACK               startup_gd32f450_470.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       504         78         78          8          0       3064   adc_app.o
       224          8          0          0          0       1585   btn_app.o
      1440        652          0          0          0       6133   command_proc.o
       316          0          0          0          0       4864   diskio.o
      2230         42          0          0         60      23924   ebtn.o
      6332         62         13          6          0      31728   ff.o
        20          4          0          0          0        505   flash_app.o
       364         28          0          0          0       2711   gd25qxx.o
       326         18          0          6          0       4462   gd30ad3344.o
      3310        448         64        645       4788      23778   gd32f470vet6_bsp.o
       654         16          0          0          0       8982   gd32f4xx_adc.o
      1332          8          0          0          0      11428   gd32f4xx_dma.o
       272          0          0          0          0       5178   gd32f4xx_gpio.o
       496         16          0          0          0       7707   gd32f4xx_i2c.o
       392         58          0          0          0     106906   gd32f4xx_it.o
       216         20          0          0          0       1528   gd32f4xx_misc.o
        20          6          0          0          0        570   gd32f4xx_pmu.o
       864         66          0          0          0       7244   gd32f4xx_rcu.o
       484         26          0          0          0       4402   gd32f4xx_rtc.o
       628        136          0          0          0      16230   gd32f4xx_sdio.o
       132          0          0          0          0       5257   gd32f4xx_spi.o
       670         18          0          0          0       8511   gd32f4xx_usart.o
        16          6          0          0          0        482   led_app.o
        16          0         29         92         32       1780   main.o
      1492         94       2712         26          0       9298   oled.o
        64         22          0          0          0        551   oled_app.o
        16          6          0          0          0        482   rtc_app.o
        92         14          0          0          0        567   scheduler.o
      6966        344          0         36         32      32036   sdio_sdcard.o
        64         26        428          0       8192        956   startup_gd32f450_470.o
       688         30          0          4          0       4135   system_gd32f4xx.o
       164         24          0          4          0      29206   systick.o
       872        198        197          0          0       3665   tf_app.o
        88          8        376          0          0       1735   unicode.o
       100         26          0          0          0       1022   usart_app.o

    ----------------------------------------------------------------------
     31904       <USER>       <GROUP>        832      13104     372612   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        40          0          9          5          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

        60          8          0          0          0         84   __0sscanf.o
        44          6          0          0          0         84   __2sprintf.o
        90          0          0          0          0          0   __dczerorl2.o
         8          0          0          0          0         68   __main.o
       392          4         17          0          0         92   __printf_flags_ss_wp.o
        14          0          0          0          0         68   __printf_wp.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        52          8          0          0          0          0   __scatter.o
        28          0          0          0          0          0   __scatter_zi.o
        28          0          0          0          0         68   _chval.o
         6          0          0          0          0          0   _printf_a.o
         6          0          0          0          0          0   _printf_c.o
        44          0          0          0          0        108   _printf_char.o
        48          6          0          0          0         96   _printf_char_common.o
        40          0          0          0          0         68   _printf_charcount.o
         6          0          0          0          0          0   _printf_d.o
       120         16          0          0          0         92   _printf_dec.o
         6          0          0          0          0          0   _printf_e.o
         6          0          0          0          0          0   _printf_f.o
      1054          0          0          0          0        216   _printf_fp_dec.o
       764          8         38          0          0        100   _printf_fp_hex.o
       128         16          0          0          0         84   _printf_fp_infnan.o
         6          0          0          0          0          0   _printf_g.o
       148          4         40          0          0        160   _printf_hex_int_ll_ptr.o
         6          0          0          0          0          0   _printf_i.o
       178          0          0          0          0         88   _printf_intcommon.o
        10          0          0          0          0          0   _printf_l.o
         6          0          0          0          0          0   _printf_lc.o
        10          0          0          0          0          0   _printf_ll.o
         6          0          0          0          0          0   _printf_lld.o
         6          0          0          0          0          0   _printf_lli.o
         6          0          0          0          0          0   _printf_llo.o
         6          0          0          0          0          0   _printf_llu.o
         6          0          0          0          0          0   _printf_llx.o
       124         16          0          0          0         92   _printf_longlong_dec.o
         6          0          0          0          0          0   _printf_ls.o
         6          0          0          0          0          0   _printf_n.o
         6          0          0          0          0          0   _printf_o.o
       112         10          0          0          0        124   _printf_oct_int_ll.o
         6          0          0          0          0          0   _printf_p.o
        78          0          0          0          0        108   _printf_pad.o
         0          0          0          0          0          0   _printf_percent.o
         4          0          0          0          0          0   _printf_percent_end.o
         6          0          0          0          0          0   _printf_s.o
        82          0          0          0          0         80   _printf_str.o
        36          0          0          0          0         84   _printf_truncate.o
         6          0          0          0          0          0   _printf_u.o
        44          0          0          0          0        108   _printf_wchar.o
       188          6          8          0          0         92   _printf_wctomb.o
         6          0          0          0          0          0   _printf_x.o
        22          0          0          0          0        100   _rserrno.o
       884          4          0          0          0        100   _scanf.o
       332          0          0          0          0         96   _scanf_int.o
        64          0          0          0          0         84   _sgetc.o
        16          0          0          0          0         68   _snputc.o
        10          0          0          0          0         68   _sputc.o
        64          0          0          0          0         92   _wcrtomb.o
       228          4        148          0          0         96   bigflt0.o
      2152        136          0          0          0        960   btod.o
        18          0          0          0          0         80   exit.o
         6          0          0          0          0        152   heapauxi.o
         0          0          0          0          0          0   indicate_semi.o
        18          0          0          0          0         76   isspace.o
        44         10        272          0          0         76   lc_ctype_c.o
        44         10         28          0          0         76   lc_numeric_c.o
         2          0          0          0          0          0   libinit.o
        34          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
        38          0          0          0          0         68   llshl.o
       138          0          0          0          0         80   lludiv10.o
        88          0          0          0          0         76   memcmp.o
        16          4          0          0          0         76   rt_ctype_table.o
         8          4          0          0          0         68   rt_errno_addr_intlibspace.o
         8          4          0          0          0         68   rt_locale_intlibspace.o
        68          0          0          0          0         68   rt_memclr.o
        78          0          0          0          0         80   rt_memclr_w.o
       138          0          0          0          0         68   rt_memcpy_v6.o
       100          0          0          0          0         80   rt_memcpy_w.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
        44          8          0          0          0         84   scanf_char.o
      1272         16          0          0          0        168   scanf_fp.o
       800         14          0          0          0        100   scanf_hexfp.o
       308         16          0          0          0        100   scanf_infnan.o
       128          0          0          0          0         68   strcmpv7m.o
        62          0          0          0          0         76   strlen.o
       150          0          0          0          0         80   strncmp.o
        12          4          0          0          0         68   sys_exit.o
        74          0          0          0          0         80   sys_stackheap_outer.o
         2          0          0          0          0         68   use_no_semi.o
        52          4          0          0          0         80   vsnprintf.o
        98          4          0          0          0        140   d2f.o
        16          4          0          0          0        116   dcheck1.o
        24          0          0          0          0        116   dcmpi.o
       120          4          0          0          0        140   deqf.o
       120          4          0          0          0        140   dleqf.o
       340         12          0          0          0        152   dmul.o
       156          4          0          0          0        140   dnaninf.o
        12          0          0          0          0        116   dretinf.o
       108          0          0          0          0        128   drleqf.o
        86          4          0          0          0        132   f2d.o
       140          4          0          0          0        132   fnaninf.o
         0          0         16          0          0          0   fpconst.o
        10          0          0          0          0        116   fpinit.o
        10          0          0          0          0        116   fretinf.o
         6          0          0          0          0        116   istatus.o
         4          0          0          0          0        116   printf1.o
         4          0          0          0          0        116   printf2.o
       100          0          0          0          0        116   retnan.o
        92          0          0          0          0        116   scalbn.o
         4          0          0          0          0        116   scanf1.o
         8          0          0          0          0        132   scanf2.o
        48          0          0          0          0        116   trapv.o
         0          0          0          0          0          0   usenofp.o
        64         16          0          0          0        248   dunder.o
        48          0          0          0          0        124   fpclassify.o
       140         22          0          0          0        132   frexp.o
       228          8          0          0          0        308   ldexp.o
       266         16          0          0          0        308   narrow.o

    ----------------------------------------------------------------------
     13884        <USER>        <GROUP>          0         96       9808   Library Totals
        20          2          3          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

     11612        350        551          0         96       6060   c_w.l
      1506         40         16          0          0       2628   fz_wm.l
       746         62          0          0          0       1120   m_wm.l

    ----------------------------------------------------------------------
     13884        <USER>        <GROUP>          0         96       9808   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     45788       2962       4508        832      13200     347740   Grand Totals
     45788       2962       4508        188      13200     347740   ELF Image Totals (compressed)
     45788       2962       4508        188          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                50296 (  49.12kB)
    Total RW  Size (RW Data + ZI Data)             14032 (  13.70kB)
    Total ROM Size (Code + RO Data + RW Data)      50484 (  49.30kB)

==============================================================================

