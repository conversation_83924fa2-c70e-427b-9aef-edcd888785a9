#include "usart_app.h"

void Usart0Task(void)
{
    if (usart0_rx_flag == 0) return;

    Usart0Printf("%s\r\n", usart0_rx_buffer_proc);

    memset(usart0_rx_buffer_proc, 0, USART0_BUFFER_SIZE);
    usart0_rx_flag = 0;
}

void Usart1Task(void)
{
    if (usart1_rx_flag == 0) return;

    CmdProc((char *)usart1_rx_buffer_proc);

    memset(usart1_rx_buffer_proc, 0, USART0_BUFFER_SIZE);
    usart1_rx_flag = 0;
}
