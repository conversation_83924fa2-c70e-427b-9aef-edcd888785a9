/*!
    \file    gd30ad3344.c
    \brief   gd30ad3344 driver
    
    \version 2024-10-08, V1.0.0, firmware for GD30AD3344
*/

#include "gd30ad3344.h"
#include "main.h"

uint16_t ADC_Config[2]={0}; 
uint16_t AD3344_CONFIG;

/*!
    \brief      delay us
    \param[in]  t: delay time
    \param[out] none
    \retval     none
*/
void delay_us(uint32_t t)
{
    uint16_t i;
    while (t--){
         i = 10;
         while(i--);
   }
}

/*!
    \brief      exti-line enable (PA6)
    \param[in]  none
    \param[out] none
    \retval     none
*/
void ad3344_Exit_enable(void)
{
//    rcu_periph_clock_enable(RCU_GPIOA);
//    rcu_periph_clock_enable(RCU_AF);
//    
//    gpio_init(GPIOA, GPIO_MODE_IN_FLOATING, GPIO_OSPEED_50MHZ, GPIO_PIN_6);
//    /* connect key wakeup EXTI line to key GPIO pin */
//    gpio_exti_source_select(GPIO_PORT_SOURCE_GPIOA, GPIO_PIN_SOURCE_6);
//    /* configure key wakeup EXTI line */
//    exti_init(EXTI_6, EXTI_INTERRUPT, EXTI_TRIG_FALLING);
//    exti_interrupt_flag_clear(EXTI_6);
//    
//    nvic_irq_enable(EXTI5_9_IRQn, 2U, 0U);
}

/*!
    \brief      exti-line disable
    \param[in]  none
    \param[out] none
    \retval     none
*/
void ad3344_Exit_disable(void)
{
//    nvic_irq_disable(EXTI5_9_IRQn);
//    exti_interrupt_flag_clear(EXTI_6);
//    exti_interrupt_disable(EXTI_6);
//    
//    rcu_periph_clock_enable(RCU_SPI0);
//    gpio_init(GPIOA, GPIO_MODE_AF_PP, GPIO_OSPEED_50MHZ, GPIO_PIN_5|GPIO_PIN_6|GPIO_PIN_7);
}

/*!
    \brief      SPI 16-bit transmit and receive
    \param[in]  data: 16-bit data to transmit
    \param[out] none
    \retval     16-bit received data
*/
uint16_t ad3344_spi_txrx16bit(uint16_t data)
{
    uint16_t rx_data = 0;

    // 发送高字节
    while(RESET == spi_i2s_flag_get(SPI0, SPI_FLAG_TBE));
    spi_i2s_data_transmit(SPI0, (data >> 8) & 0xFF);
    while(RESET == spi_i2s_flag_get(SPI0, SPI_FLAG_RBNE));
    rx_data = (spi_i2s_data_receive(SPI0) << 8);

    // 发送低字节
    while(RESET == spi_i2s_flag_get(SPI0, SPI_FLAG_TBE));
    spi_i2s_data_transmit(SPI0, data & 0xFF);
    while(RESET == spi_i2s_flag_get(SPI0, SPI_FLAG_RBNE));
    rx_data |= spi_i2s_data_receive(SPI0);

    return rx_data;
}

/*!
    \brief      GD30AD3344 transmit data
    \param[in]  config_d: register value
    \param[out] none
    \retval     the read value of register
*/
uint16_t AD3344_Send_Data(uint16_t config_d)
{
    uint16_t Data;

    Data = ad3344_spi_txrx16bit(config_d);

    return (Data);
}

/*!
    \brief      GD30AD3344 Config Register(32bit trans)
    \param[in]  config_d: the data need to be tramit
    \param[in]  *config: Register readback value
    \param[out] none
    \retval     the read value of register
*/
uint16_t ad3344_read_data32(uint16_t config_d, uint16_t *config)
{
    uint16_t data;
    
    data = AD3344_Send_Data(config_d);
    *config = AD3344_Send_Data(0);
    
    return (data);
}

/*!
    \brief      GD30AD3344 Config Register(16bit trans)
    \param[in]  config_d: the data need to be tramit
    \param[out] none
    \retval     the read value of register
*/
uint16_t ad3344_read_data16(uint16_t config_d)
{
    uint16_t data;

    // 第一次传输：发送配置并启动转换
    SPI_CLR_CS();
    delay_us(2000);  // 增加CS建立时间

    AD3344_Send_Data(config_d);  // 发送配置，启动转换

    SPI_SET_CS();
    delay_us(50000);  // 大幅增加转换等待时间到50ms

    // 第二次传输：读取转换结果
    SPI_CLR_CS();
    delay_us(2000);  // 增加CS建立时间

    data = AD3344_Send_Data(0x0000);  // 发送0读取数据

    SPI_SET_CS();
    delay_us(2000);

    return (data);
}

/*!
    \brief      GD30AD3344 Read Register
    \param[in]  addr
      \arg      0x01: Config Register
    \param[out] none
    \retval     the read value of register
*/
uint16_t ad3344_read_regs(uint8_t addr)
{
    uint8_t reg_addr = addr;
    uint16_t reg_rtu = 0;

    SPI_CLR_CS();
    delay_us(1000);
    
    AD3344_Send_Data(reg_addr);
    
    reg_rtu = AD3344_Send_Data(0x00);
    
    SPI_SET_CS();
    delay_us(10000);
    
    SPI_CLR_CS();


    return reg_rtu;
}

/*!
    \brief      GD30AD3344 Init
    \param[in]  none
    \param[out] none
    \retval     none
*/
void ad3344_init(uint16_t config_d)
{
    // 确保CS初始状态为高
    SPI_SET_CS();
    delay_us(1000);

    // 开始配置传输
    SPI_CLR_CS();
    delay_us(1000);

    #ifdef BIT32_TRANS_CYCLE
    ad3344_read_data32(config_d, ADC_Config);
    #else
    ad3344_spi_txrx16bit(config_d);
    #endif

    // 结束传输
    SPI_SET_CS();
    delay_us(1000);

    // 保存当前配置
    AD3344_CONFIG = config_d;
}

/*!
    \brief      GD30AD3344 stop conversion
    \param[in]  none
    \param[out] none
    \retval     none
*/
void ad3344_stop_conver()
{
    AD3344_CONFIG |= AD3344_REG_CONFIG_MODE_SINGLE;
    
    #ifdef BIT32_TRANS_CYCLE
    ad3344_read_data32(AD3344_CONFIG, ADC_Config);
    #else
    ad3344_spi_txrx16bit(AD3344_CONFIG);
    #endif
}

/*!
    \brief      GD30AD3344 reset
    \param[in]  none
    \param[out] none
    \retval     the result of the conversion
*/
void ad3344_reset()
{
    #ifdef BIT32_TRANS_CYCLE
    ad3344_read_data32(AD3344_CONFIG_DEFAULT, ADC_Config);
    #else
    ad3344_spi_txrx16bit(AD3344_CONFIG_DEFAULT);
    #endif
}
