#include "gd32f470vet6_bsp.h"

Gd30Channel_t gd30_channels[4] = 
{
    {0xC3AB, "AIN0", 2.0f, 0.0f},
    {0xD3AB, "AIN1", 2.0f, 0.0f},
    {0xE3AB, "AIN2", 2.0f, 0.0f},
    {0xF3AB, "AIN3", 2.0f, 0.0f}
};

Config_t config_data[3] =
{
    {1.99f, 10.11f},
    {2.99f, 20.11f},
    {3.99f, 30.11f}
};

SampleStatus_t sample_status = {0, 0, 0};
uint8_t sample_cycle = 5;

uint8_t device_id[32] = {0};

int main(void)
{
    SysInit();

    // TfCardTest();
    Flash_ReadDeviceId();

    while(1)
    {
        TaskExeution();
    }
}
