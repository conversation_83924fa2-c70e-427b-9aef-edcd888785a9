#include "adc_app.h"

typedef enum
{
    GD30_IDLE = 0,
    GD30_START_CONV,
    GD30_READ_DATA
} Gd30State_t;

Gd30State_t gd30_state = GD30_IDLE;
uint8_t current_channel = 0;
uint32_t conv_start_time = 0;

void SampleProc(void)
{
    sample_status.last_sample_time = ucRtc.second;

    Usart1Printf("report:20%hhu-%02hhu-%02hhu %02hhu:%02hhu:%02hhu ch0=%.2f,ch1=%.2f,ch2=%.2f\r\n", 
                 ucRtc.year, ucRtc.month, ucRtc.date, ucRtc.hour, ucRtc.minute, ucRtc.second,
                 gd30_channels[0].voltage, gd30_channels[1].voltage, gd30_channels[2].voltage);
}

void SampleTask(void)
{
    if (sample_status.enable == 0) return;

    if (ucRtc.second == (sample_status.last_sample_time + sample_cycle) % 60)
    {
        SampleProc();
    }
}

void Gd30Task(void)
{
    uint16_t value = 0;
    float voltage_raw = 0.0f;

    switch(gd30_state)
    {
        case GD30_IDLE:
            current_channel = 0;
            gd30_state = GD30_START_CONV;
            break;

        case GD30_START_CONV:
            if(current_channel < 4)
            {
                ad3344_init(gd30_channels[current_channel].config);
                gd30_state = GD30_READ_DATA;
            }
            else
            {
                gd30_state = GD30_IDLE;
            }
            break;

        case GD30_READ_DATA:
            value = ad3344_read_data16(gd30_channels[current_channel].config);
            voltage_raw = (float)value * 4.096f / 65536.0f;
            gd30_channels[current_channel].voltage = voltage_raw * gd30_channels[current_channel].calibration;

            current_channel++;
            gd30_state = GD30_START_CONV;
            break;
    }
}

void AdcTask(void)
{
    uint32_t adc_sum = 0;

    for (uint8_t i = 0; i < 32; i++)
    {
        adc_sum += adc0_value[i];
    }

    adc0_voltage = (float)adc_sum / 32.0f * 3.3f / 4095.0f;
}
